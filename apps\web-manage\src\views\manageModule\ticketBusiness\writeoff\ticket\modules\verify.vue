<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { getVerificationInfo } from '#/api/manageModule';
import { Divider, Image, InputNumber, message, Modal } from 'ant-design-vue';
import { verificationAction } from '#/api/manageModule';

const emits = defineEmits(['success']);

const [Model, modelApi] = useVbenModal({
  confirmText: '确认核销',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    const data = modelApi.getData<any>();
    if (isOpen) {
      params.value = { verificationCode: data.verificationCode };
      getLogList();
    }
  },
});
const params = ref<any>({
  verificationCode: '',
});
const orderInfo = ref<any>({});
const getLogList = async () => {
  const res = await getVerificationInfo(params.value);
  orderInfo.value = res;
};

const num = ref(1);

const selectData = ref<any>({});

// 提交核销
const handleSubmit = async () => {
  let submitData = {
    orderId: orderInfo.value.orderItemInfo?.orderId,
    orderItemId: orderInfo.value.orderItemInfo?.orderItemId,
    orderDetailId: orderInfo.value.orderDetail?.id,
    childOrderDetailId: selectData.value.id || null,
    num: num.value,
  };
  console.log(submitData, '123');
  let content = '是否确定核销1张门票？';
  if (orderInfo.value.orderDetail?.model !== 1) {
    content = `是否确定核销门票【${selectData.value.ticketName}】？`;
  }
  Modal.confirm({
    title: '确认核销',
    content: content,
    onOk: () => {
      verificationAction(submitData).then(() => {
        emits('success');
        message.success('核销成功');
        modelApi.close();
      });
    },
  });
};

// 检查票据是否可以被选中
const canSelectTicket = (item: any) => {
  // 套票：剩余可核销次数为0不能被选中
  if (orderInfo.value.orderDetail?.model === 2) {
    return item.canVerificationNum > 0;
  }

  // 时效卡、次数卡、畅游卡：isLimit==1时，核销总次数不能大于限制次数
  if (
    [3, 4, 5].includes(orderInfo.value.orderDetail?.model) &&
    item.isLimit === 1
  ) {
    // 检查总次数限制
    if (item.totalLimit > 0 && item.totalVerifyCount >= item.totalLimit) {
      return false;
    }
    // 检查每月次数限制
    if (item.monthLimit > 0 && item.monthVerifyCount >= item.monthLimit) {
      return false;
    }
    // 检查每日次数限制
    if (item.dayLimit > 0 && item.todayVerifyCount >= item.dayLimit) {
      return false;
    }
  }

  return true;
};

const selectTicket = (data: any) => {
  // 检查是否可以选中
  if (!canSelectTicket(data)) {
    return;
  }
  console.log(data);
  selectData.value = data;
};
</script>
<template>
  <Model class="w-[1000px]" title="核销">
    <div class="flex justify-between gap-5">
      <div class="flex-1">
        <h3 class="mb-3 text-[16px] font-bold">订单信息</h3>
        <div
          class="mb-5 rounded-[5px] bg-[#f5f5f5] p-3 text-[14px] text-[#333]"
        >
          <p class="leading-[20px]">
            核销码：{{ orderInfo.orderDetail?.verificationCode }}
          </p>
          <p class="leading-[20px]">
            有效期：{{
              orderInfo.orderItemInfo?.validType == 1
                ? orderInfo.orderItemInfo?.validBeginDate + '当天有效'
                : orderInfo.orderItemInfo?.validBeginDate
                  ? orderInfo.orderItemInfo?.validBeginDate +
                    '至' +
                    orderInfo.orderItemInfo?.validEndDate
                  : ''
            }}
          </p>
        </div>
        <div class="flex">
          <div class="flex-shrink-0 rounded-md shadow-sm">
            <Image
              :src="orderInfo.orderItemInfo?.ticketCover"
              width="150px"
              height="150px"
              alt=""
            />
          </div>
          <div class="ml-4 text-[14px] leading-6">
            <h3 class="mb-3 text-[16px] font-bold">
              {{ orderInfo.orderItemInfo?.ticketName }}
            </h3>
            <p>
              联系人：<span>{{ orderInfo.orderInfo?.userName }}</span>
              <span class="ml-2">{{ orderInfo.orderInfo?.userPhone }}</span>
            </p>
            <p>
              出行人：<span>{{ orderInfo.orderDetail?.touristName }}</span>
              <span class="ml-2">{{
                orderInfo.orderDetail?.touristPhone
              }}</span>
            </p>
            <p>
              包含子票：{{
                orderInfo.childOrderDetailList
                  ?.map((item: any) => item.ticketName)
                  .join('，')
              }}
            </p>
            <p v-if="orderInfo.orderDetail?.model == 4">
              总次数限制：{{
                orderInfo.orderDetail?.cardLimitUseNum > 0
                  ? orderInfo.orderDetail?.cardLimitUseNum + '次'
                  : '不限'
              }}
            </p>
          </div>
        </div>
        <!-- 普通门票显示次数 -->
        <div
          class="mt-5 flex items-center justify-around"
          v-if="orderInfo.orderDetail?.model == 1"
        >
          <div class="text-center">
            <p class="text-[20px] font-bold leading-[1.8]">
              {{ orderInfo.orderDetail?.verificationTotal }}
            </p>
            <p class="text-[14px]">总核销次数</p>
          </div>
          <Divider
            type="vertical"
            style="height: 20px; background-color: #dcdfe6"
          />
          <div class="text-center">
            <p class="text-[20px] font-bold leading-[1.8]">
              {{ orderInfo.orderDetail?.canVerificationNum }}
            </p>
            <p class="text-[14px]">剩余核销次数</p>
          </div>
        </div>
      </div>
      <!-- 普通门票 -->
      <div class="flex-1" v-if="orderInfo.orderDetail?.model == 1">
        <h3 class="mb-3 text-[16px] font-bold">输入核销次数</h3>
        <InputNumber
          v-model:value="num"
          :defaultValue="1"
          :min="1"
          :precision="0"
          :max="orderInfo.orderDetail?.canVerificationNum"
          placeholder="请输入核销次数"
          class="w-[200px]"
        ></InputNumber>
      </div>
      <!-- 套票 -->
      <div class="flex-1" v-if="orderInfo.orderDetail?.model == 2">
        <h3 class="mb-3 text-[16px] font-bold">选择门票核销</h3>
        <div class="grid grid-cols-2 gap-3">
          <div
            v-for="(item, index) in orderInfo.childOrderDetailList"
            :key="index"
            class="box-border rounded-lg p-3 text-[14px] leading-[20px]"
            :class="[
              canSelectTicket(item)
                ? 'cursor-pointer bg-[#f5f6f9]'
                : 'cursor-not-allowed bg-[#f0f0f0] opacity-60',
              selectData.id === item.id
                ? 'border-[2px] border-[#1a73e8]'
                : 'border-[2px] border-[#f5f6f9]',
            ]"
            @click="selectTicket(item)"
          >
            <p class="mb-2 font-bold">{{ item.ticketName }}</p>
            <p class="mb-1">可核销次数：{{ item.verificationTotal }}</p>
            <p>剩余可核销：{{ item.canVerificationNum }}</p>
            <p v-if="!canSelectTicket(item)" class="mt-1 text-xs text-red-500">
              剩余次数不足，无法核销
            </p>
          </div>
        </div>
      </div>
      <!-- 时效卡，次数卡，畅游卡 -->
      <div
        class="flex-2"
        v-if="[3, 4, 5].includes(orderInfo.orderDetail?.model)"
      >
        <h3 class="mb-3 text-[16px] font-bold">选择门票核销</h3>
        <div class="grid grid-cols-2 gap-3">
          <div
            v-for="(item, index) in orderInfo.childOrderDetailList"
            :key="index"
            class="box-border rounded-lg p-3 text-[14px] leading-[20px]"
            :class="[
              canSelectTicket(item)
                ? 'cursor-pointer bg-[#f5f6f9]'
                : 'cursor-not-allowed bg-[#f0f0f0] opacity-60',
              selectData.id === item.id
                ? 'border-[2px] border-[#1a73e8]'
                : 'border-[2px] border-[#f5f6f9]',
            ]"
            @click="selectTicket(item)"
          >
            <p class="mb-2 font-bold">{{ item.ticketName }}</p>
            <p v-if="item.isLimit == 0">限制：不限次数</p>
            <p v-else>
              限制：共{{ item.totalLimit }}次，每月{{
                item.monthLimit
              }}次，每日{{ item.dayLimit }}次
            </p>
            <p class="mt-1" v-if="item.isLimit == 1">
              核销：总{{ item.totalVerifyCount }}次，当月{{
                item.monthVerifyCount
              }}次，当日{{ item.todayVerifyCount }}次
            </p>
            <!-- 显示不可选中的原因 -->
            <div
              v-if="!canSelectTicket(item) && item.isLimit == 1"
              class="mt-1 text-xs text-red-500"
            >
              <p
                v-if="
                  item.totalLimit > 0 &&
                  item.totalVerifyCount >= item.totalLimit
                "
              >
                总次数已达上限
              </p>
              <p
                v-else-if="
                  item.monthLimit > 0 &&
                  item.monthVerifyCount >= item.monthLimit
                "
              >
                本月次数已达上限
              </p>
              <p
                v-else-if="
                  item.dayLimit > 0 && item.todayVerifyCount >= item.dayLimit
                "
              >
                今日次数已达上限
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Model>
</template>
<style lang="scss" scoped></style>
