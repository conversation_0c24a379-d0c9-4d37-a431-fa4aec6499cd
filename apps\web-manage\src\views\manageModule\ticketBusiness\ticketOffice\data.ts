import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import {
  getAllScenicList,
  getAllHandsetList,
  getAllSelfTicketList,
} from '#/api/manageModule';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '售票点名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入售票点名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'salesPointType',
      label: '售票点类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择售票点类型',
        allowClear: true,
        options: accessAllEnums.value.salesPointType.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: accessAllEnums.value.status.list,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'pointName',
      title: '售票点名称',
    },
    {
      field: 'salesPointType',
      title: '售票点类型',
      formatter: ({ row }: any) => {
        return accessAllEnums.value.salesPointType.list.find(
          (item: any) => item.value === row.salesPointType,
        )?.label;
      },
    },
    {
      field: 'scenicName',
      title: '所属景区',
      formatter: ({ row }: any) => row.scenicInfo.scenicName || '--',
    },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: onStatusChange,
        },
      },
    },
    {
      field: 'createdBy',
      title: '创建人',
    },
    {
      field: 'createdAt',
      title: '创建时间',
    },
    {
      field: 'operation',
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'pointName',
          nameTitle: '售票点',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [{ code: 'ticket', text: '可售门票' }, 'edit', 'delete'],
      },
      fixed: 'right',
      title: '操作',
      width: 180,
    },
  ];
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'pointName',
      label: '售票点名称',
      componentProps: {
        placeholder: '请输入售票点名称',
        allowClear: true,
      },
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      fieldName: 'salesPointType',
      label: '售票点类型',
      componentProps: {
        placeholder: '请选择售票点类型',
        allowClear: true,
        options: accessAllEnums.value.salesPointType.list,
      },
      rules: 'selectRequired',
    },
    {
      component: 'RadioGroup',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: accessAllEnums.value.status.list,
      },
      defaultValue: 1,
    },
    {
      component: 'ApiSelect',
      fieldName: 'ticketIds',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '可售门票',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择可售门票',
        allowClear: true,
        mode: 'multiple',
      },
      // dependencies: {
      //   componentProps(values) {
      //     if (values.scenicId) {
      //       return (values.ticketIds = []);
      //     }
      //     return {};
      //   },
      //   triggerFields: ['scenicId'],
      // },
    },
    {
      component: 'ApiSelect',
      fieldName: 'userIds',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '售票员',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择售票员',
        allowClear: true,
        mode: 'multiple',
      },
      dependencies: {
        if: (formData) => formData.salesPointType !== 2,
        triggerFields: ['salesPointType'],
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'ticketMachineIds',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '关联自助机',
      rules: 'selectRequired',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.deviceName,
            value: item.id,
            sn: item.deviceSn,
          }));
        },
        // 菜单接口
        api: getAllSelfTicketList,
        placeholder: '请选择关联自助机',
        allowClear: true,
        mode: 'multiple',
        alwaysLoad: true,
      },
      dependencies: {
        if: (formData) => formData.salesPointType === 2,
        componentProps(values) {
          if (values.scenicId) {
            return {
              params: {
                scenicId: values.scenicId + ',0',
              },
            };
          }
          return {};
        },
        triggerFields: ['salesPointType', 'scenicId'],
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'ticketMachineIds',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '关联手持机',
      rules: 'selectRequired',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.deviceName,
            value: item.id,
            sn: item.deviceSn,
          }));
        },
        // 菜单接口
        api: getAllHandsetList,
        placeholder: '请选择关联手持机',
        allowClear: true,
        mode: 'multiple',
        alwaysLoad: true,
      },
      dependencies: {
        if: (formData) => formData.salesPointType === 3,
        componentProps(values) {
          if (values.scenicId) {
            return {
              params: {
                scenicId: values.scenicId + ',0',
              },
            };
          }
          return {};
        },
        triggerFields: ['salesPointType', 'scenicId'],
      },
    },
  ];
}
