import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getCustomizeRefundList(params: Recordable<any>) {
  return requestClient.get('/tkt/customizeRefund/list', { params });
}

async function getCustomizeRefundInfo(id: string) {
  return requestClient.get('/tkt/customizeRefund/info', { params: { id } });
}

async function getCustomizeRefundRefundLog(params: Recordable<any>) {
  return requestClient.get('/tkt/customizeRefund/log', { params });
}

export { getCustomizeRefundList, getCustomizeRefundInfo, getCustomizeRefundRefundLog };
