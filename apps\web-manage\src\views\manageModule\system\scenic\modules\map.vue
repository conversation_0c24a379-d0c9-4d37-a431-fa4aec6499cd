<template>
  <MapModal class="w-[50%]">
    <div class="flex justify-between">
      <Form layout="inline" :model="formInfo" class="mb-2">
        <FormItem label="经度" name="longitude">
          <Input readonly v-model:value="formInfo.longitude" />
        </FormItem>
        <FormItem label="纬度" name="latitude">
          <Input readonly v-model:value="formInfo.latitude" />
        </FormItem>
      </Form>
      <div class="flex items-center">
        <Input v-model:value="searchValue" placeholder="请输入地址" />
        <Button type="primary" @click="searchPlace">搜索</Button>
      </div>
    </div>
    <div id="tcMap" class="h-[500px] w-full"></div>
  </MapModal>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';
import { Form, FormItem, Input, Button, message } from 'ant-design-vue';
import { useVbenModal } from '@vben/common-ui';
import { loadScript } from '#/utils/TencentMap';

const emit = defineEmits<{
  (e: 'confirm'): void;
}>();

const formInfo = ref({
  longitude: '',
  latitude: '',
});

let mapInstance, geocoder, markers, mapEdit; // 存储地图实例用于销毁和复用

const [MapModal, mapModalApi] = useVbenModal({
  title: '获取定位',
  async onConfirm() {
    emit('confirm', { ...formInfo.value, address: searchValue.value });
    mapModalApi.close();
  },
  onOpenChange(isOpen) {
    const data = mapModalApi.getData<any>();
    if (isOpen) {
    //   console.log(data, 'data');
      formInfo.value.longitude = data.longitude;
      formInfo.value.latitude = data.latitude;
      searchValue.value = data.address;
      nextTick(() => {
        handleOpenMap(data);
      });
    } else {
      searchValue.value = '';
      formInfo.value.longitude = '';
      formInfo.value.latitude = '';
      handleCloseMap();
    }
  },
});

// 搜索地址
const searchValue = ref('');
const searchPlace = () => {
  if (!searchValue.value) {
    message.warning('请输入地址');
    return;
  }

  geocoder
    ?.getLocation({ address: searchValue.value })
    .then((res) => {
      if (res.status === 0) {
        formInfo.value.longitude = res.result.location.lng;
        formInfo.value.latitude = res.result.location.lat;
        markers.updateGeometries([
          {
            id: 'marker',
            styleId: 'marker',
            draggable: true,
            position: new window.TMap.LatLng(
              res.result.location.lat,
              res.result.location.lng,
            ),
          },
        ]);
        mapInstance?.setCenter(res.result.location);
        mapInstance?.setZoom(15);
        console.log(markers, 'markers');
      }
    })
    .catch((err) => {
      console.log(err, 'err');
      if (err.status != 0) {
        message.error('地址匹配失败！');
      }
    });
};

// 搜索地址
const searchAddress = (e) => {
  geocoder.getAddress({ location: e.position }).then((res) => {
    searchValue.value = res.result.formatted_addresses.standard_address;
    formInfo.value.latitude = res.result.location.lat;
    formInfo.value.longitude = res.result.location.lng;
  });
};

// 加载地图脚本并初始化地图
const handleOpenMap = async () => {
  try {
    await loadScript();
    await initMap();
  } catch (error) {
    console.error('腾讯地图脚本加载失败:', error);
  }
};

// 销毁地图实例
const handleCloseMap = () => {
  if (mapInstance) {
    mapInstance.destroy();
    mapInstance = null;
  }
};

// 初始化地图
const initMap = () => {
  if (!window.TMap) {
    console.error('TMap 未定义');
    return;
  }
  let center;
  if (formInfo.value.latitude && formInfo.value.longitude) {
    center = new window.TMap.LatLng(
      formInfo.value.latitude,
      formInfo.value.longitude,
    );
  } else {
    center = new window.TMap.LatLng(39.984104, 116.307503);
  }
  mapInstance = new window.TMap.Map('tcMap', {
    center: center,
  });

  geocoder = new window.TMap.service.Geocoder();

  markers = new window.TMap.MultiMarker({
    map: mapInstance,
    styles: {
      marker: new TMap.MarkerStyle({
        width: 30,
        height: 45,
        anchor: { x: 15, y: 45 },
      }),
    },
    geometries: [
      {
        id: 'marker',
        position: center,
        styleId: 'marker',
        draggable: true,
      },
    ],
  });

  mapEdit = new window.TMap.tools.GeometryEditor({
    map: mapInstance,
    overlayList: [
      {
        overlay: markers, // 可编辑图层
        id: 'marker',
        selectedStyleId: 'highlight',
      },
    ],
    actionMode: window.TMap.tools.constants.EDITOR_ACTION.INTERACT, // 编辑器的工作模式
    activeOverlayId: 'marker', // 激活图层
    selectable: true,
  });
  mapEdit.on('adjust_complete', function (e) {
    searchAddress(e);
  });
};
</script>

<style scoped lang="scss"></style>
