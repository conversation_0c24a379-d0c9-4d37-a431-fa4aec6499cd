<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import { Button, message, Tag, Modal } from 'ant-design-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import {
  getTicketList,
  getTicketInfo,
  deleteTicket,
  changeTicketStatus,
} from '#/api/manageModule';
import { useAccessStore } from '@vben/stores';
import { toRefs, ref } from 'vue';
import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const { accessAllEnums } = toRefs(useAccessStore());

const [FormModel, formModelApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const onCreate = () => {
  disabled.value = false;
  formModelApi.setData({}).open();
};
const onEdit = async (row: any) => {
  const res = await getTicketInfo(row.id);
  formModelApi.setData(res).open();
};

const onDelete = async (row: any) => {
  const hideLoading = message.loading({
    content: '删除中...',
    duration: 0,
    key: 'action_process_msg',
  });
  deleteTicket(row.id)
    .then(() => {
      message.success('删除成功');
      onRefresh();
      hideLoading();
    })
    .catch(() => {
      hideLoading();
    });
};
const onRefresh = () => {
  gridApi.query();
};

const disabled = ref<any>(false);
const onActionClick = async ({ code, row }: { code: string; row: any }) => {
  disabled.value = false;
  if (code === 'edit') {
    onEdit(row);
  } else if (code === 'delete') {
    onDelete(row);
  } else if (code === 'info') {
    disabled.value = true;
    const res = await getTicketInfo(row.id);
    formModelApi.setState({
      showConfirmButton: false,
      cancelText: '关闭',
    });
    formModelApi.setData(res).open();
  } else if (code === 'copy') {
    const res = await getTicketInfo(row.id);
    formModelApi
      .setData({
        ...res,
        id: '',
      })
      .open();
  }
};

const onStatusChange = async (newStatus: any, row: any) => {
  const status: Recordable<string> = {
    0: '禁用',
    1: '启用',
  };
  try {
    await confirm(
      `你要将门票【${row.ticketName}】的状态切换为 【${status[newStatus.toString()]}】 吗？`,
      `切换状态`,
    );
    await changeTicketStatus({ id: row.id, status: newStatus });
    return true;
  } catch {
    return false;
  }
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
     collapsedRows: 1,
    wrapperClass: 'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    headerCellConfig: {
      height: 40,
    },
    cellConfig: {
      height: 70,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await getTicketList({
            page: page.currentPage,
            pageSize: page.pageSize,
            model: 2,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
const confirm = (content: string, title: string) => {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
};

//
const filterSaleResource = (saleResource: any) => {
  return accessAllEnums.value.ticketSaleResource.list.find(
    (item: any) => item.value === saleResource,
  )?.label;
};
</script>
<template>
  <Page auto-content-height>
    <FormModel :disabled="disabled" @success="onRefresh" />
    <Grid>
      <template #CellTags="{ row }">
        <Tag color="processing" v-for="tag in row.ticketTags" :key="tag">
          {{ tag }}
        </Tag>
      </template>
      <template #CellSaleResource="{ row }">
        <div class="flex flex-wrap gap-1">
          <Tag color="processing" v-for="tag in row.saleResource" :key="tag">{{
            filterSaleResource(tag)
          }}</Tag>
        </div>
      </template>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          新增套票
        </Button>
      </template>
    </Grid>
  </Page>
</template>
