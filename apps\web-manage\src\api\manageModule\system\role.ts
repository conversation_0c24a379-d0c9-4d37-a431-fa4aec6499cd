import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 获取角色列表数据
 */
async function getRoleList(params: Recordable<any>) {
  return requestClient.get('/sys/adminRole/list', {
    params,
  });
}

/**
 * 获取所有角色列表数据
 */
async function getAllRoleList(params: Recordable<any>) {
  return requestClient.get('/sys/adminRole/all', {
    params,
  });
}

/**
 * 获取角色权限数据
 */
async function getRolePermissions(params: Recordable<any>) {
  return requestClient.get('/sys/adminRole/permissions', { params });
}

/**
 * 创建角色
 * @param data 角色数据
 */
async function createRole(data: Recordable<any>) {
  return requestClient.post('/sys/adminRole/create', data);
}
/**
 * 更新角色权限
 * @param data 角色权限数据
 */
async function updateRolePermissions(data: Recordable<any>) {
  return requestClient.post('/sys/adminRole/permissions', data);
}

/**
 * 更新角色
 *
 * @param id 角色 ID
 * @param data 角色数据
 */
async function updateRole(data: Recordable<any>) {
  return requestClient.post('/sys/adminRole/update', data);
}

/**
 * 更新角色
 *
 * @param id 角色 ID
 * @param data 角色数据
 */
async function changeRoleStatus(data: Recordable<any>) {
  return requestClient.post('/sys/adminRole/changeStatus', data);
}

/**
 * 删除角色
 * @param id 角色 ID
 */
async function deleteRole(id: string) {
  return requestClient.post('/sys/adminRole/delete', { id });
}

export {
  createRole,
  deleteRole,
  getRoleList,
  updateRole,
  changeRoleStatus,
  getAllRoleList,
  getRolePermissions,
  updateRolePermissions,
};
