import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getHandsetList(params: Recordable<any>) {
  return requestClient.get('/eqp/hand/list', { params });
}
async function getAllHandsetList(params: Recordable<any>) {
  return requestClient.get('/eqp/hand/all', { params });
}
async function getHandsetInfo(id: string) {
  return requestClient.get('/eqp/hand/info', { params: { id } });
}

async function createHandset(data: Recordable<any>) {
  return requestClient.post('/eqp/hand/create', data);
}

async function updateHandset(data: Recordable<any>) {
  return requestClient.post('/eqp/hand/update', data);
}

async function deleteHandset(id: string) {
  return requestClient.post('/eqp/hand/delete', { id });
}

async function changeHandsetStatus(data: Recordable<any>) {
  return requestClient.post('/eqp/hand/changeStatus', data);
}

async function getHandsetVerificationList(params: any) {
  return requestClient.get('/eqp/hand/verificationList', { params });
}

async function getHandsetTicketOrderList(params: any) {
  return requestClient.get('/eqp/hand/ticketOrder', { params });
}

export {
  getHandsetList,
  getAllHandsetList,
  getHandsetInfo,
  createHandset,
  updateHandset,
  deleteHandset,
  changeHandsetStatus,
  getHandsetVerificationList,
  getHandsetTicketOrderList,
};
