<script setup lang="ts">
import { useVbenForm } from '#/adapter/form';
import { usePasswordFormSchema } from '../data';
import { updateAdminPassword } from '#/api/manageModule';
import { message } from 'ant-design-vue';

const [PasswordForm, passwordFormApi] = useVbenForm({
  schema: usePasswordFormSchema(),
  // 一行显示1个
  wrapperClass: 'grid-cols-1',

  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'flex w-[500px]',
    },
  },
  handleSubmit: onSubmit,
  actionWrapperClass: 'flex justify-center',
});

async function onSubmit(values: any) {
  console.log(values, 'submit');
  const res = await updateAdminPassword(values);
  message.success('操作成功');
  passwordFormApi.resetForm();
}
</script>
<template>
  <div class="max-w-[650px] p-5">
    <PasswordForm ref="form" />
  </div>
</template>
