import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getScenicList(params: Recordable<any>) {
  return requestClient.get('/sys/scenic/list', { params });
}
async function getAllScenicList(params: Recordable<any>) {
  return requestClient.get('/sys/scenic/all', { params });
}
async function getScenicInfo(id: string) {
  return requestClient.get('/sys/scenic/info', { params: { id } });
}

async function createScenic(data: Recordable<any>) {
  return requestClient.post('/sys/scenic/create', data);
}

async function updateScenic(data: Recordable<any>) {
  return requestClient.post('/sys/scenic/update', data);
}

async function deleteScenic(id: string) {
  return requestClient.post('/sys/scenic/delete', { id });
}

async function changeScenicStatus(data: Recordable<any>) {
  return requestClient.post('/sys/scenic/changeStatus', data);
}

export {
  getScenicList,
  getAllScenicList,
  getScenicInfo,
  createScenic,
  updateScenic,
  deleteScenic,
  changeScenicStatus,
};
