<script setup lang="ts">
import { computed, ref, h, watch } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Button, Tag } from 'ant-design-vue';
import TimeChange from './timeChange.vue';
import StockChange from './stockChange.vue';

const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:value']);
const timeList = ref<any[]>([]);

watch(
  () => props.value,
  (newVal) => {
    timeList.value = newVal;
  },
  {
    immediate: true,
    deep: true,
  },
);

// 时间设置
const [ModelTime, modelTimeApi] = useVbenModal({
  connectedComponent: TimeChange,
  destroyOnClose: false,
});
const handleConfirm = (timeData: any) => {
  timeList.value = timeData;
  emit('update:value', timeList.value);
};
// 库存设置
const [ModelStock, modelStockApi] = useVbenModal({
  connectedComponent: StockChange,
  destroyOnClose: false,
});
const handleStockConfirm = (stockData: any) => {
  timeList.value = stockData.map((item: any) => {
    return {
      beginTime: item.beginTime,
      endTime: item.endTime,
      periodStock: item.periodStock,
      periodPrice: item.periodPrice,
    };
  });
  emit('update:value', timeList.value);
};
</script>

<template>
  <div class="min-h-[40px] w-full rounded-md border border-gray-300 p-2">
    <div
      class="flex flex-wrap items-center gap-2 p-2"
      v-if="timeList.length > 0"
    >
      <div v-for="item in timeList" :key="item.beginTime">
        <Tag color="blue">{{ item.beginTime }} - {{ item.endTime }}</Tag>
      </div>
    </div>
    <div class="flex items-center justify-center">
      <Button
        type="link"
        :disabled="disabled"
        @click="modelTimeApi.setData(timeList).open()"
        >编辑时间</Button
      >
      <Button
        type="link"
        :disabled="disabled"
        @click="modelStockApi.setData(timeList).open()"
        v-if="timeList.length > 0"
        >编辑库存</Button
      >
    </div>

    <ModelTime @confirm="handleConfirm" />
    <ModelStock @confirm="handleStockConfirm" />
  </div>
</template>
