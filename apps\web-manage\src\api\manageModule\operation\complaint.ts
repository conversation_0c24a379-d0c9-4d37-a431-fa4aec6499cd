import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getComplaintList(params: Recordable<any>) {
  return requestClient.get('/op/complaint/list', { params });
}
async function getComplaintInfo(params: Recordable<any>) {
  return requestClient.get('/op/complaint/info', { params });
}

async function complaintDeal(data: Recordable<any>) {
  return requestClient.post('/op/complaint/deal', data);
}

export { getComplaintList, getComplaintInfo, complaintDeal };
