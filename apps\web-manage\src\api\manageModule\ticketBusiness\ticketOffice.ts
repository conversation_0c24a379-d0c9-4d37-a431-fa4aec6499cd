import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getTicketOfficeList(params: Recordable<any>) {
  return requestClient.get('/tkt/ticketOffice/list', { params });
}

async function getAllTicketOfficeList(params: Recordable<any>) {
  return requestClient.get('/tkt/ticketOffice/all', { params });
}

async function getTicketOfficeInfo(id: string) {
  return requestClient.get('/tkt/ticketOffice/info', { params: { id } });
}

async function createTicketOffice(data: Recordable<any>) {
  return requestClient.post('/tkt/ticketOffice/create', data);
}

async function updateTicketOffice(data: Recordable<any>) {
  return requestClient.post('/tkt/ticketOffice/update', data);
}

async function deleteTicketOffice(id: string) {
  return requestClient.post('/tkt/ticketOffice/delete', { id });
}

async function changeTicketOfficeStatus(data: Recordable<any>) {
  return requestClient.post('/tkt/ticketOffice/changeStatus', data);
}

export {
  getTicketOfficeList,
  getAllTicketOfficeList,
  getTicketOfficeInfo,
  createTicketOffice,
  updateTicketOffice,
  deleteTicketOffice,
  changeTicketOfficeStatus,
};
