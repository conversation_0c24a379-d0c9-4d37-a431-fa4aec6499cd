<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useSendFormSchema } from '../data';
import { distributeCoupon } from '#/api/manageModule';
import { Button, message } from 'ant-design-vue';
import SelectUser from './selectUser/index.vue';
import SelectCoupon from './selectCoupon/index.vue';


const emit = defineEmits(['success']);
const formData = ref<any>({});
// 表单配置
const [Form, formApi] = useVbenForm({
    commonConfig: {
        // 所有表单项
        labelWidth: 120,
        componentProps: {
            class: 'w-full',
        },
    },
    fieldMappingTime: [],
    scrollToFirstError: true,
    schema: useSendFormSchema(),
    showDefaultActions: false,
    // 一行显示2个，小屏一行显示1个
    wrapperClass: 'grid-cols-1',
});
// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
    async onConfirm() {
        await handleSubmit();
    },
    onOpenChange(isOpen) {
        const data = modelApi.getData<any>();
        formApi.resetForm();
        if (isOpen) {
            if (data) {
                // 创建新对象而不是直接赋值
                const newFormData = { ...data };
                // 赋值给formData.value
                formData.value = newFormData;
                id.value = data.id;
                formApi.setValues(formData.value);
            }
        }
    },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
    const { valid } = await formApi.validate();
    if (!valid) return null;

    const values = await formApi.getValues();
    if (values.userIdList && values.userIdList.length) {
        values.userIdList = values.userIdList.map((item: any) => item.id);
    }
    if (values.couponIdList && values.couponIdList.length) {
        values.couponIdList = values.couponIdList.map((item: any) => item.id);
    }

    return values;
};

// 统一的提交处理函数
const handleSubmit = async () => {
    const values = await processFormValues();
    console.log(values, 'submit');
    if (!values) return;
    modelApi.lock();
    try {
        await distributeCoupon({ ...values });
        message.success('发放成功');
        emit('success');
        modelApi.close();
    } catch (error) {
        modelApi.unlock();
    }
};
</script>

<template>
    <Model class="w-[50%]" title="发放优惠券">
        <Form>
            <template #userIdList="slotProps">
                <SelectUser v-bind="slotProps"></SelectUser>
            </template>
            <template #couponIdList="slotProps">
                <SelectCoupon v-bind="slotProps"></SelectCoupon>
            </template>
        </Form>
    </Model>
</template>
