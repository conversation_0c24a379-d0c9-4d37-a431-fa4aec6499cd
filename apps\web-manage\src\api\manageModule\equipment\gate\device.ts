import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getGateList(params: Recordable<any>) {
  return requestClient.get('/eqp/gate/list', { params });
}
async function getAllGateList(params: Recordable<any>) {
  return requestClient.get('/eqp/gate/all', { params });
}
async function getGateInfo(id: string) {
  return requestClient.get('/eqp/gate/info', { params: { id } });
}

async function createGate(data: Recordable<any>) {
  return requestClient.post('/eqp/gate/create', data);
}

async function updateGate(data: Recordable<any>) {
  return requestClient.post('/eqp/gate/update', data);
}

async function deleteGate(id: string) {
  return requestClient.post('/eqp/gate/delete', { id });
}

async function changeGateStatus(data: Recordable<any>) {
  return requestClient.post('/eqp/gate/changeStatus', data);
}

async function getGateVerificationList(params: any) {
  return requestClient.get('/eqp/gate/verificationList', { params });
}

export {
  getGateList,
  getAllGateList,
  getGateInfo,
  createGate,
  updateGate,
  deleteGate,
  changeGateStatus,
  getGateVerificationList,
};
