import type { Recordable } from '@vben/types';
import { requestClient } from '#/api/request';

/**
 * 获取系统配置
 */
async function getConfigList(params: Recordable<any>) {
  return requestClient.get('/sys/config/list', { params });
}

/**
 * 获取单个配置
 */
async function getConfigInfoByScene(params: Recordable<any>) {
  return requestClient.get('/sys/config/infoByScene', { params });
}

export { getConfigList, getConfigInfoByScene };
