<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { ref, watch, computed } from 'vue';
import {
  RadioGroup,
  RadioButton,
  Button,
  Table,
  TableSummaryRow,
  TableSummaryCell,
  TypographyText,
  InputNumber,
} from 'ant-design-vue';
import { useVbenModal } from '@vben/common-ui';
import TicketList from './ticketList.vue';

const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['update:value']);
const ticketList = ref<any[]>([]);
const isDisabled = computed(() => props.disabled);
const totoalPrice = computed(() => {
  return ticketList.value.reduce((acc, cur) => {
    return acc + Number(cur.sellingPrice);
  }, 0);
});
watch(
  () => props.value,
  (newVal) => {
    ticketList.value = newVal;
  },
  {
    immediate: true,
    deep: true,
  },
);

const columns = ref<any>([
  {
    title: '景区',
    dataIndex: 'scenicName',
    width: 120,
  },
  {
    title: '票名',
    dataIndex: 'ticketName',
    width: 150,
  },
  {
    title: '销售价',
    dataIndex: 'sellingPrice',
    width: 100,
  },
  {
    title: '次数',
    dataIndex: 'isLimit',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 80,
  },
]);

// 门票列表弹窗
const [ModelTicket, modelTicketApi] = useVbenModal({
  connectedComponent: TicketList,
  destroyOnClose: true,
});

const listChange = (data: any) => {
  // 如果data中存在ticketList中的数据，则替换
  let list = data.filter((item: any) => {
    return !changeData(ticketList.value).some(
      (ticket: any) => ticket.id === item.id,
    );
  });
  ticketList.value = [...ticketList.value, ...list];
  emit('update:value', ticketList.value);
};

const addTicket = () => {
  modelTicketApi.setData({}).open();
};
const delTicket = (val: any) => {
  let data = {
    ...val,
    id: val.id || val.childTicketId,
  };
  ticketList.value = changeData(ticketList.value).filter(
    (item: any) => item.id !== data.id,
  );
  emit('update:value', ticketList.value);
};
const changeData = (val: any) => {
  let list = val.map((item: any) => {
    return {
      ...item,
      id: item.id ? item.id : item.childTicketId,
    };
  });
  return list;
};

defineExpose({
  addTicket,
});
</script>
<template>
  <div class="w-full">
    <Table
      :columns="columns"
      :dataSource="ticketList"
      :pagination="false"
      bordered
      rowClassName="custom-row"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'isLimit'">
          <div class="flex items-center">
            <RadioGroup
              v-model:value="record.isLimit"
              button-style="solid"
              :disabled="isDisabled"
            >
              <RadioButton :value="0">不限次数</RadioButton>
              <RadioButton :value="1">限制次数</RadioButton>
            </RadioGroup>
            <div class="ml-5 flex items-center" v-if="record.isLimit === 1">
              共
              <InputNumber
                v-model:value="record.totalLimit"
                :precision="0"
                :min="0"
                class="w-[80px]"
                :disabled="disabled"
              />
              次， 每月最多
              <InputNumber
                v-model:value="record.monthLimit"
                :precision="0"
                :min="0"
                class="w-[80px]"
                :disabled="disabled"
              />
              次，每天最多
              <InputNumber
                v-model:value="record.dayLimit"
                :precision="0"
                :min="0"
                class="w-[80px]"
                :disabled="disabled"
              />
              次
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <Button
            type="link"
            danger
            :disabled="isDisabled"
            class="p-0"
            @click="delTicket(record)"
            >删除</Button
          >
        </template>
      </template>
      <template #summary>
        <TableSummaryRow>
          <TableSummaryCell :col-span="2">合计</TableSummaryCell>
          <TableSummaryCell :col-span="3">
            <TypographyText type="danger">
              {{ totoalPrice }}
            </TypographyText>
          </TableSummaryCell>
        </TableSummaryRow>
      </template>
    </Table>
    <ModelTicket
      :selectedTickets="changeData(ticketList)"
      @change="listChange"
    ></ModelTicket>
  </div>
</template>
<style>
.custom-row .ant-table-cell {
  padding: 6px 16px !important;
}
</style>
