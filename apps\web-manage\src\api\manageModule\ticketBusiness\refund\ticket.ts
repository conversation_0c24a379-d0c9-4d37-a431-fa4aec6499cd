import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getOrderRefundList(params: Recordable<any>) {
  return requestClient.get('/tkt/refund/list', { params });
}

async function getRefundInfo(id: string) {
  return requestClient.get('/tkt/refund/info', { params: { id } });
}

async function getOrderRefundLog(params: Recordable<any>) {
  return requestClient.get('/tkt/refund/log', { params });
}

export { getOrderRefundList, getRefundInfo, getOrderRefundLog };
