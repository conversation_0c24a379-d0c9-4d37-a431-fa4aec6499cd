<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { computed, ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useFormSchema } from '../data';
import { createGate, updateGate } from '#/api/manageModule';
const emits = defineEmits(['success']);

const formData = ref<Recordable<any>>({});
// 表单配置
const [Form, formApi] = useVbenForm({
    scrollToFirstError: true,
    schema: useFormSchema(),
    showDefaultActions: false,
    // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2',
    commonConfig: {
        // 所有表单项
        labelWidth: 120,
        componentProps: {
            class: 'w-full',
        },
    },
});

// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
    async onConfirm() {
        await handleSubmit();
    },
    onOpenChange(isOpen) {
        const data = modelApi.getData<any>();
        formApi.resetForm();
        if (isOpen) {
            if (data) {
                // 创建新对象而不是直接赋值
                const newFormData = { ...data };
                // 赋值给formData.value
                formData.value = newFormData;
                id.value = data.id;
                formApi.setValues(formData.value);
            }
        }
    },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
    const { valid } = await formApi.validate();
    if (!valid) return null;

    const values = await formApi.getValues();

    return values;
};

// 提交表单
const handleSubmit = async () => {
    const values = await processFormValues();
    if (!values) return;
    modelApi.lock();
    try {
        if (formData.value.id) {
            await updateGate({ id: id.value, ...values });
        } else {
            await createGate(values);
        }
        emits('success');
        modelApi.close();
    } catch (error) {
        modelApi.unlock();
    }
};

// 获取表单标题
const getModelTitle = computed(() => {
    return formData.value.id ? '编辑闸机' : '新增闸机';
});
</script>
<template>
    <Model class="w-[50%]" :title="getModelTitle">
        <Form>
        </Form>
    </Model>
</template>
