import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs, markRaw } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '姓名',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入姓名',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '手机号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入手机号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'idCard',
      label: '身份证号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入身份证号',
        allowClear: true,
      },
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'name',
      title: '游客姓名',
      width: 120,
    },
    {
      field: 'phone',
      title: '游客手机号',
      width: 120,
    },
    {
      field: 'idcard',
      title: '游客身份证号',
      minWidth: 150,
    },
    {
      field: 'birthday',
      title: '游客生日',
      minWidth: 100,
    },
    {
      field: 'sex',
      title: '性别',
      minWidth: 80,
      formatter: ({ row }: any) => {
        return row.sex === 0 ? '未知' : row.sex === 1 ? '男' : '女';
      },
    },
    {
      field: 'userName',
      title: '用户姓名',
      slots: { default: 'userName' },
      minWidth: 120,
    },
    {
      field: 'userPhone',
      title: '用户手机号',
      slots: { default: 'userPhone' },
      minWidth: 120,
    },
    {
      field: 'isDefault',
      title: '是否默认',
      minWidth: 80,
      formatter: ({ row }: any) => {
        return row.isDefault === 1 ? '是' : '否';
      },
    },
    {
      field: 'createdAt',
      title: '创建时间',
      minWidth: 200,
    },
    {
      field: 'operation',
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: '操作',
      width: 120,
    },
  ];
}

const type: any = {
  7: 'default',
  8: 'warning',
  9: 'success',
  11: 'error',
  12: 'default',
};
