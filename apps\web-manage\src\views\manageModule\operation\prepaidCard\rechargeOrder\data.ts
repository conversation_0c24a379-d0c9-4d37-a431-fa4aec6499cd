import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'cardNo',
      label: '卡号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入卡号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'orderNo',
      label: '订单号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入订单号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'orderSource',
      label: '订单来源',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择订单来源',
        allowClear: true,
        options: accessAllEnums.value?.orderSource.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'payMethod',
      label: '支付方式',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择支付方式',
        allowClear: true,
        options: accessAllEnums.value?.orderPayMethod.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'orderStatus',
      label: '订单状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择订单状态',
        allowClear: true,
        options: accessAllEnums.value?.orderStatus.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'payStatus',
      label: '付款状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择付款状态',
        allowClear: true,
        options: accessAllEnums.value?.orderPayStatus.list,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'dateRange',
      label: '交易时间',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['交易开始日期', '交易结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'cardNo',
      title: '卡号',
      minWidth: 150,
      fixed: 'left',
      slots: { default: 'cardNo' },
    },
    {
      field: 'cardType',
      title: '类型',
      width: 120,
      slots: { default: 'cardType' },
    },
    {
      field: 'name',
      title: '姓名',
      width: 150,
      slots: { default: 'name' },
    },
    {
      field: 'phone',
      title: '手机号',
      width: 150,
      slots: { default: 'phone' },
    },
    {
      field: 'amount',
      title: '充值金额',
      width: 150,
      slots: { default: 'amount' },
    },
    {
      field: 'orderNo',
      title: '订单号',
      width: 180,
    },
    {
      field: 'orderSource',
      title: '订单来源',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.orderSource.list.find(
          (item: any) => item.value === row.orderSource,
        )?.label;
      },
    },
    {
      field: 'payMethod',
      title: '支付方式',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.orderPayMethod.list.find(
          (item: any) => item.value === row.payMethod,
        )?.label;
      },
    },
    {
      field: 'orderStatus',
      title: '订单状态',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.orderStatus.list.find(
          (item: any) => item.value === row.orderStatus,
        )?.label;
      },
    },
    {
      field: 'payStatus',
      title: '付款状态',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.orderPayStatus.list.find(
          (item: any) => item.value === row.payStatus,
        )?.label;
      },
    },
    {
      field: 'createdAt',
      title: '交易时间',
      width: 180,
    },
  ];
}
