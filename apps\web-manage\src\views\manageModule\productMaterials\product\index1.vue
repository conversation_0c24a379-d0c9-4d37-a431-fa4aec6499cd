<template>
  <Page auto-content-height>
    <div class="flex h-full w-full">
      <div class="mr-3 w-[320px] overflow-hidden rounded-md bg-white p-4">
        <div class="mb-3 w-full">
          <Button type="primary" class="w-full" @click="onCreateType(null)"
            >新增一级类别</Button
          >
        </div>
        <div
          class="h-[calc(100%-45px)] overflow-y-auto [-ms-overflow-style:auto] [scrollbar-width:thin] [&::-webkit-scrollbar]:hidden"
        >
          <Spin :spinning="loadingType">
            <VbenTree
              :tree-data="typeList"
              :default-expanded-level="1"
              :bordered="false"
              :allowClear="true"
              value-field="id"
              label-field="typeName"
              @select="onSelect"
            >
              <template #node="{ value }">
                <div class="group flex w-full items-center justify-between">
                  <span class="truncate">{{ value.typeName }}</span>
                  <div class="item-center hidden group-hover:flex">
                    <IconifyIcon
                      icon="mdi:plus"
                      width="16"
                      @click.stop="onCreateType(value)"
                    />
                    <IconifyIcon
                      icon="mdi:edit"
                      class="mx-2"
                      @click.stop="onEditType(value)"
                    />
                    <IconifyIcon
                      icon="mdi:delete"
                      @click.stop="onDeleteType(value)"
                    />
                  </div>
                </div>
              </template>
            </VbenTree>
          </Spin>
        </div>
      </div>
      <div class="h-full flex-1 overflow-hidden">
        <Grid table-title="">
          <template #cellImages="{ row }">
            <!-- {{ row.productImg }} -->
            <Image
              :src="row.productImg.split(',')[0]"
              :preview="{ visible: false }"
              @click="previewImages(row.productImg.split(','))"
            />
          </template>
          <template #toolbar-tools>
            <Button type="primary" @click="onCreate">
              <Plus class="size-5" />
              新增产品
            </Button>
          </template>
        </Grid>
      </div>
    </div>
    <!-- 图片预览 -->
    <div style="display: none">
      <ImagePreviewGroup
        :preview="{ visible, onVisibleChange: (vis) => (visible = vis) }"
      >
        <Image v-for="item in imageList" :src="item" />
      </ImagePreviewGroup>
    </div>
    <FormModel @success="onRefresh"></FormModel>
    <addTypeModel @success="resetTypeList"></addTypeModel>
  </Page>
</template>
<script setup lang="ts">
import type { DataNode } from 'ant-design-vue/es/tree';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import { IconifyIcon } from '@vben/icons';
import { Page, VbenTree, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import {
  Button,
  message,
  Modal,
  Spin,
  Image,
  ImagePreviewGroup,
} from 'ant-design-vue';
import { computed, ref } from 'vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  allType,
  deleteType,
  getList,
  deleteProduct,
} from '#/api/productMaterials/materials';
import { $t } from '#/locales';
import { useColumns, useGridFormSchema } from './data';
import form from './modules/form.vue';
import addType from './modules/addType.vue';

// 图片预览
const visible = ref(false);
const imageList = ref<any>([]);
const previewImages = (images: any) => {
  imageList.value = images;
  visible.value = true;
};

const [FormModel, formModelApi] = useVbenModal({
  connectedComponent: form,
  destroyOnClose: true,
});
const [addTypeModel, addTypeModelApi] = useVbenModal({
  connectedComponent: addType,
  destroyOnClose: true,
});
const loadingType = ref(false);
const typeList = ref<any>([]);
const currentType = ref<any>(null);
async function loadTypeList() {
  loadingType.value = true;
  try {
    const res = await allType({ mode: 1 });
    typeList.value = res as unknown as DataNode[];
  } finally {
    loadingType.value = false;
  }
}
loadTypeList();
const resetTypeList = () => {
  message.success('操作成功');
  loadTypeList();
};
const onCreateType = (val: any) => {
  if (val) {
    addTypeModelApi.setData({ parentId: val.id }).open();
  } else {
    addTypeModelApi.setData(null).open();
  }
};
const onEditType = (val: any) => {
  addTypeModelApi.setData(val).open();
};
const onDeleteType = (val: any) => {
  Modal.confirm({
    content: `你要删除【${val.typeName}】吗？`,
    onOk: async () => {
      try {
        await deleteType({ id: val.id, mode: 1 });
        message.success('删除成功');
        loadTypeList();
      } catch (error) {
        message.error('删除失败');
      }
    },
  });
};
const onSelect = (selectedKeys: any) => {
  console.log('selected', selectedKeys);
  if (currentType.value && currentType.value === selectedKeys._id) {
    currentType.value = null;
  } else {
    currentType.value = selectedKeys._id;
  }
  gridApi.query({
    typeId: currentType.value,
  });
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 2,
    wrapperClass: 'grid grid-cols-4 md:grid-cols-4 gap-4',
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res: any = await getList({
            page: page.currentPage,
            pageSize: page.pageSize,
            mode: 1,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

function onActionClick(e: OnActionClickParams<any>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
  }
}

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
function confirm(content: string, title: string) {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
}

function onEdit(row: any) {
  console.log(row, 'row');
  formModelApi.setData(row).open();
}

function onDelete(row: any) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.name]),
    duration: 0,
    key: 'action_process_msg',
  });
  deleteProduct({ id: row.id, mode: 1 })
    .then(() => {
      message.success('删除成功');
      onRefresh();
      hideLoading();
    })
    .catch(() => {
      hideLoading();
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formModelApi.setData({}).open();
}
</script>
