<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import {
  Checkbox,
  Input,
  Row,
  Col,
  InputNumber,
  Divider,
  QRCode,
} from 'ant-design-vue';

const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['update:value']);
// 配置项状态
let config = reactive({
  // 页面设置
  page: {
    width: 58, // 小票纸宽度(mm) - 常见58mm热敏纸
    marginTop: 2,
    marginBottom: 2,
    marginLeft: 2,
    marginRight: 2,
  },
  // 抬头设置
  header: {
    scenicName: true,
  },
  // 门票信息
  info: {
    ticketCode: true,
    ticketNumber: true,
    ticketName: true,
    personTime: true,
    price: true,
    sum: true,
    //有效期
    validTime: true,
  },
  // 售票信息
  sell: {
    ticketSeller: true,
    ticketPoint: true,
    printTime: true,
  },
  // 尾注设置
  footer: {
    endnote: true,
    endnoteText: '',
  },
});

// 计算预览区样式 - 按实际小票比例
const previewStyle = computed(() => ({
  width: `${config.page.width * 4}px`, // 58mm * 4 = 232px
  padding: `${config.page.marginTop * 4}px ${config.page.marginRight * 4}px ${config.page.marginBottom * 4}px ${config.page.marginLeft * 4}px`,
  fontFamily: 'monospace', // 使用等宽字体模拟小票打印效果
}));

watch(
  () => config,
  (newConfig) => {
    emit('update:value', {
      margins: [
        newConfig.page.marginLeft * 4,
        newConfig.page.marginTop * 4,
        newConfig.page.marginRight * 4,
        newConfig.page.marginBottom * 4,
      ],
      width: newConfig.page.width * 4,
      templateConfig: newConfig,
    });
  },
  { deep: true, immediate: true },
);
watch(
  () => props.value,
  (newValue) => {
    if (newValue && newValue.templateConfig) {
      Object.assign(config, newValue.templateConfig);
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
</script>

<template>
  <div class="flex gap-6">
    <!-- 左侧预览区 -->
    <div class="w-1/3">
      <h3 class="mb-4 text-lg font-semibold">模板设计</h3>
      <div
        class="flex min-h-[600px] items-start justify-center border border-gray-300 bg-gray-50 p-4"
      >
        <!-- 小票预览 -->
        <div class="border bg-white shadow-lg" :style="previewStyle">
          <!-- 抬头 -->
          <div v-if="config.header.scenicName" class="mb-1 text-center">
            <div
              v-if="config.header.scenicName"
              class="text-[14px] font-bold leading-[1.8]"
            >
              演示景区
            </div>
          </div>

          <!-- 门票信息 -->
          <div class="text-[14px] leading-normal">
            <div v-if="config.info.ticketCode" class="mb-2 flex justify-center">
              <QRCode value="门票码" :size="100" type="svg" :bordered="false" />
            </div>
            <div v-if="config.info.ticketNumber" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >票号</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">1234567890</span>
            </div>
            <div v-if="config.info.ticketName" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >票名</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">门票名称</span>
            </div>
            <div v-if="config.info.personTime" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >人次</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">1</span>
            </div>
            <div v-if="config.info.price" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >单价</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">100.00</span>
            </div>
            <div v-if="config.info.sum" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >金额</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">100.00</span>
            </div>
            <div v-if="config.info.validTime" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >有效期</span
              >
              <span class="opacity-50">：</span>

              <span class="flex-1">2025-10-23</span>
            </div>
          </div>

          <!-- 分割线 -->
          <Divider
            v-if="
              config.info.ticketNumber ||
              config.info.ticketName ||
              config.info.personTime ||
              config.info.price ||
              config.info.sum ||
              config.info.validTime
            "
            class="!my-2 !text-[14px]"
            dashed
          />

          <!-- 售票信息 -->
          <div class="text-[14px] leading-normal">
            <div
              v-if="config.sell.ticketSeller || config.sell.ticketPoint"
              class="mb-1.5 text-center font-medium"
            >
              售票信息
            </div>
            <div v-if="config.sell.ticketSeller" class="mb-1.5 text-center">
              售票员：XXX
            </div>
            <div v-if="config.sell.ticketPoint" class="mb-1.5 text-center">
              售票点：XXXXX
            </div>
          </div>

          <!-- 分割线 -->
          <Divider
            v-if="config.sell.ticketSeller || config.sell.ticketPoint"
            class="!my-2 !text-[14px]"
            dashed
          />

          <div class="text-[14px] leading-normal" v-if="config.sell.printTime">
            <div class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >打印时间</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">{{ new Date().toLocaleString() }}</span>
            </div>
          </div>

          <!-- 分割线 -->
          <Divider
            v-if="config.sell.printTime && config.footer.endnote"
            class="!my-2 !text-[14px]"
            dashed
          />

          <!-- 尾注 -->
          <div
            v-if="config.footer.endnote"
            class="text-center text-[14px] leading-tight"
          >
            <div v-if="config.footer.endnote" class="mb-1">
              {{
                config.footer.endnoteText ||
                '尾注：尾注限制100字，居中对齐，超出换行'
              }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧配置区 -->
    <div class="w-2/3">
      <div class="space-y-6">
        <!-- 页面设置 -->
        <div>
          <h4 class="mb-3 font-semibold">页面设置</h4>
          <Row :gutter="16">
            <Col :span="24">
              <div class="mb-2 flex items-center gap-2">
                <span class="w-18 text-[14px]">纸张宽度：</span>
                <InputNumber
                  v-model:value="config.page.width"
                  :min="40"
                  :max="80"
                  size="small"
                />
                <span class="text-[14px] text-gray-500">mm (常用: 58mm/80mm)</span>
              </div>
            </Col>
          </Row>
          <Row :gutter="8">
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-15 text-[14px]">上边距：</span>
                <InputNumber
                  v-model:value="config.page.marginTop"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-[14px] text-gray-500">mm</span>
              </div>
            </Col>
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-15 text-[14px]">下边距：</span>
                <InputNumber
                  v-model:value="config.page.marginBottom"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-[14px] text-gray-500">mm</span>
              </div>
            </Col>
          </Row>
          <Row :gutter="8" class="mt-2">
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-15 text-[14px]">左边距：</span>
                <InputNumber
                  v-model:value="config.page.marginLeft"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-[14px] text-gray-500">mm</span>
              </div>
            </Col>
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-15 text-[14px]">右边距：</span>
                <InputNumber
                  v-model:value="config.page.marginRight"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-[14px] text-gray-500">mm</span>
              </div>
            </Col>
          </Row>
        </div>

        <!-- 抬头设置 -->
        <div>
          <h4 class="mb-3 font-semibold">抬头设置</h4>
          <Row :gutter="16">
            <Col :span="6">
              <Checkbox v-model:checked="config.header.scenicName"
                >景区名称</Checkbox
              >
            </Col>
          </Row>
        </div>

        <!-- 门票信息 -->
        <div>
          <h4 class="mb-3 font-semibold">门票信息</h4>
          <Row :gutter="16">
            <Col :span="6">
              <Checkbox v-model:checked="config.info.ticketCode"
                >门票码</Checkbox
              >
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.info.ticketNumber"
                >票号</Checkbox
              >
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.info.ticketName">票名</Checkbox>
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.info.personTime">人次</Checkbox>
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.info.price">单价</Checkbox>
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.info.sum">金额</Checkbox>
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.info.validTime"
                >有效期</Checkbox
              >
            </Col>
          </Row>
        </div>

        <!-- 售票信息 -->
        <div>
          <h4 class="mb-3 font-semibold">售票信息</h4>
          <Row :gutter="16">
            <Col :span="6">
              <Checkbox v-model:checked="config.sell.ticketSeller"
                >售票员</Checkbox
              >
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.sell.ticketPoint"
                >售票点</Checkbox
              >
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.sell.printTime"
                >打印时间</Checkbox
              >
            </Col>
          </Row>
        </div>

        <!-- 尾注设置 -->
        <div>
          <h4 class="mb-3 font-semibold">尾注设置</h4>
          <div class="flex items-center gap-2">
            <Checkbox v-model:checked="config.footer.endnote" class="w-[80px]"
              >尾注</Checkbox
            >
            <Input
              v-model:value="config.footer.endnoteText"
              placeholder="请输入尾注内容"
              size="small"
              :maxlength="100"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
