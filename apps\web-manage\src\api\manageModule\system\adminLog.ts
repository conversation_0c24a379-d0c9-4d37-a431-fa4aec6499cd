import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getLoginLogList(params: Recordable<any>) {
  return requestClient.get('/sys/adminUserLoginLog/getList', { params });
}
async function getActionLogList(params: Recordable<any>) {
  return requestClient.get('/sys/adminUserActionLog/allList', { params });
}
async function getActionLogOneList(params: Recordable<any>) {
  return requestClient.get('/sys/adminUserActionLog/oneList', { params });
}

export { getLoginLogList, getActionLogList, getActionLogOneList };