import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getCouponList(params: Recordable<any>) {
  return requestClient.get('/mkt/coupon/list', { params });
}
async function getCouponInfo(params: Recordable<any>) {
  return requestClient.get('/mkt/coupon/info', { params });
}
async function createCoupon(data: Recordable<any>) {
  return requestClient.post('/mkt/coupon/create', data);
}

async function updateCoupon(data: Recordable<any>) {
  return requestClient.post('/mkt/coupon/update', data);
}

/**
 * 撤销优惠券
 * @param id 优惠券 ID
 */
async function revokeCoupon(id: string) {
  return requestClient.post('/mkt/coupon/revoke', { id });
}

async function changeCouponStatus(data: Recordable<any>) {
  return requestClient.post('/mkt/coupon/changeStatus', data);
}

async function getCouponLogList(params: Recordable<any>) {
  return requestClient.get('/mkt/couponLog/list', { params });
}
/**
 * 撤销优惠券发放记录
 * @param id 优惠券发放记录 ID
 */
async function revokeCouponLog(id: string) {
  return requestClient.post('/mkt/couponLog/revoke', { id });
}

/**
 * 分发优惠券
 * @param data 分发优惠券数据
 */
async function distributeCoupon(data: Recordable<any>) {
  return requestClient.post('/mkt/coupon/distribute', data);
}

export {
  getCouponList,
  getCouponInfo,
  createCoupon,
  updateCoupon,
  revokeCoupon,
  changeCouponStatus,
  getCouponLogList,
  revokeCouponLog,
  distributeCoupon,
};
