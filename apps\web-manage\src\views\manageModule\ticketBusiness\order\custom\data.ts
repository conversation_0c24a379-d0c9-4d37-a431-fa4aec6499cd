import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

import type { TableColumnType } from 'ant-design-vue';

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'orderName',
      label: '订单名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入订单名称',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'orderNo',
      label: '订单号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入订单号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'orderSource',
      label: '订单来源',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择订单来源',
        allowClear: true,
        options: accessAllEnums.value?.ticketOrderSource.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'payMethod',
      label: '支付方式',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择支付方式',
        allowClear: true,
        options: accessAllEnums.value?.orderPayMethod.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'payStatus',
      label: '付款状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择付款状态',
        allowClear: true,
        options: accessAllEnums.value?.orderPayStatus.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'orderStatus',
      label: '订单状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择订单状态',
        allowClear: true,
        options: accessAllEnums.value?.ticketOrderStatus.list,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'orderDate',
      label: '下单日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['下单开始日期', '下单结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'orderNo',
      title: '订单号',
      minWidth: 150,
      fixed: 'left',
    },
    {
      field: 'orderName',
      title: '订单名称',
      minWidth: 120,
    },
    {
      field: 'orderTime',
      title: '下单时间',
      width: 150,
    },
    {
      field: 'payTime',
      title: '支付时间',
      width: 150,
    },
    {
      field: 'orderPrice',
      title: '订单金额',
      width: 150,
    },
    {
      field: 'orderSource',
      title: '订单来源',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.orderSource.list.find(
          (item: any) => item.value === row.orderSource,
        )?.label;
      },
    },
    {
      field: 'payMethod',
      title: '支付方式',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.orderPayMethod.list.find(
          (item: any) => item.value === row.payMethod,
        )?.label;
      },
    },
    {
      field: 'orderStatus',
      title: '订单状态',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.orderStatus.list.find(
          (item: any) => item.value === row.orderStatus,
        )?.label;
      },
    },
    {
      field: 'payStatus',
      title: '支付状态',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.orderPayStatus.list.find(
          (item: any) => item.value === row.payStatus,
        )?.label;
      },
    },
    {
      field: 'sellerInfo',
      title: '售票员',
      width: 150,
      slots: { default: 'sellerInfo' },
    },
    {
      field: 'operation',
      title: '操作',
      width: 120,
      slots: { default: 'operation' },
      align: 'center',
      fixed: 'right',
    },
  ];
}

export function useLogTableSchema(): TableColumnType[] {
  return [
    {
      title: '操作内容',
      dataIndex: 'actionContent',
      minWidth: 150,
      align: 'center',
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      width: 180,
      customRender: ({ record }: any) => {
        return accessAllEnums.value?.orderStatus.list.find(
          (item: any) => item.value === record.orderStatus,
        )?.label;
      },
      align: 'center',
    },
    {
      title: '操作管理员',
      dataIndex: 'ticketName',
      width: 180,
      align: 'center',
    },
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      width: 180,
      align: 'center',
    },
  ];
}

export function useRefundFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'InputNumber',
      fieldName: 'refundPrice',
      label: '退款金额',
      rules: 'required',
      componentProps: {
        placeholder: '请输入退款金额',
        allowClear: true,
      },
    },
    {
      component: 'Textarea',
      fieldName: 'refundReason',
      label: '退款原因',
      rules: 'required',
      componentProps: {
        placeholder: '请输入退款原因',
        allowClear: true,
        rows: 3,
      },
    },
  ];
}
