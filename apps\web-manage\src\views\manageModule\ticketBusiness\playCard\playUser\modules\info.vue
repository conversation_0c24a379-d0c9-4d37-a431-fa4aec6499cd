<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Descriptions, DescriptionsItem, Image, Table } from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
import { getUserCardVerificationRecord } from '#/api/manageModule';
import { useVerifyTableSchema } from '../data';

const cardInfo = ref<any>({});
const [Model, modelApi] = useVbenModal({
  title: '游玩卡详情',
  showCancelButton: false,
  confirmText: '关闭',
  async onConfirm() {
    modelApi.close();
  },
  onOpenChange(isOpen) {
    let data = modelApi.getData();
    if (isOpen) {
      cardInfo.value = data;
      params.value.cardId = data.id;
      getLog();
    }
  },
});

const params = ref<any>({
  cardId: '',
  page: 1,
  pageSize: 10,
});
const logList = ref<any>([]);
const logTotal = ref<any>(0);
const getLog = async () => {
  const res = await getUserCardVerificationRecord(params.value);
  logList.value = res.list;
  logTotal.value = res.total;
};

const filterStatus = (val: any) => {
  return accessAllEnums.value?.ticketCardStatus.list.find(
    (item: any) => item.value === val,
  )?.label;
};
</script>
<template>
  <Model class="w-[50%]">
    <Descriptions title="" bordered :column="2">
      <DescriptionsItem label="卡号">{{ cardInfo.cardNo }}</DescriptionsItem>
      <DescriptionsItem label="游玩卡名称">{{
        cardInfo.cardName
      }}</DescriptionsItem>
      <DescriptionsItem label="游玩卡状态">{{
        filterStatus(cardInfo.cardStatus)
      }}</DescriptionsItem>
      <DescriptionsItem label="持卡人">{{
        cardInfo.cardUserName
      }}</DescriptionsItem>
      <DescriptionsItem label="持卡人手机号">{{
        cardInfo.cardUserPhone
      }}</DescriptionsItem>
      <DescriptionsItem label="持卡人身份证">{{
        cardInfo.cardUserIdCard
      }}</DescriptionsItem>
      <DescriptionsItem label="人脸信息" :span="2">
        <Image
          v-if="cardInfo.faceImg"
          :src="cardInfo.faceImg"
          style="width: 80px; height: 80px"
        />
      </DescriptionsItem>
      <DescriptionsItem label="购买时间">{{
        cardInfo.orderTime
      }}</DescriptionsItem>
      <DescriptionsItem label="有效期">{{
        cardInfo.validBeginDate + '~' + cardInfo.validEndDate
      }}</DescriptionsItem>
    </Descriptions>
    <div class="mt-6">
      <h3 class="mb-3 font-bold">使用记录</h3>
      <Table
        :columns="useVerifyTableSchema()"
        :dataSource="logList"
        :pagination="{
          current: params.page,
          pageSize: params.pageSize,
          total: logTotal,
          onChange: (page, pageSize) => {
            params.page = page;
            params.pageSize = pageSize;
            getLog();
          },
          showTotal: (total) => `共 ${total} 条`,
        }"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex == 'adminUserInfo'">
            <p v-if="record.adminUserInfo">
              {{ record.adminUserInfo?.name }}<br />{{
                record.adminUserInfo?.phone
              }}
            </p>
            <p v-else>--</p>
          </template>
        </template>
      </Table>
    </div>
  </Model>
</template>
