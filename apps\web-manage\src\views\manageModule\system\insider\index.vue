<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type {
    OnActionClickParams,
    VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteInsider, getInsiderList, changeInsiderStatus, getInsiderInfo } from '#/api/manageModule';
import { $t } from '#/locales';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const [FormModal, formModalApi] = useVbenModal({
    connectedComponent: Form,
    destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
    formOptions: {
        fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
        schema: useGridFormSchema(),
        collapsed: true,
        submitOnChange: true,
        submitOnEnter: true,
        collapsedRows: 1,
        wrapperClass: 'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
        showCollapseButton: true,
    },
    gridOptions: {
        columns: useColumns(onActionClick, onChangeStatus),
        height: 'auto',
        keepSource: true,
        headerCellConfig: {
            height: 40,
        },
        cellConfig: {
            height: 70,
        },
        proxyConfig: {
            ajax: {
                query: async ({ page }, formValues) => {
                    const res: any = await getInsiderList({
                        page: page.currentPage,
                        pageSize: page.pageSize,
                        ...formValues,
                    });
                    return {
                        items: res.list,
                        total: res.total,
                    };
                },
            },
        },
        rowConfig: {
            keyField: 'id',
        },

        toolbarConfig: {
            custom: true,
            export: false,
            refresh: { code: 'query' },
            search: true,
            zoom: true,
        },
    } as VxeTableGridOptions<any>,
});

function onActionClick(e: OnActionClickParams<any>) {
    switch (e.code) {
        case 'delete': {
            onDelete(e.row);
            break;
        }
        case 'edit': {
            onEdit(e.row);
            break;
        }
    }
}

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
function confirm(content: string, title: string) {
    return new Promise((reslove, reject) => {
        Modal.confirm({
            content,
            onCancel() {
                reject(new Error('已取消'));
            },
            onOk() {
                reslove(true);
            },
            title,
        });
    });
}

/**
 *
 * @param row 更改状态
 */

async function onChangeStatus(
    newStatus: any,
    row: any,
) {
    const status: any = {
        1: '启用',
        0: '禁用',
    };
    try {
        await confirm(
            `你要将内部人员【${row.name}】的状态切换为 【${status[newStatus.toString()]}】 吗？`,
            `切换状态`,
        );
        await changeInsiderStatus({ id: row.id, status: newStatus });
        message.success(`内部人员【${row.name}】已切换为【${status[newStatus.toString()]}】`);
        onRefresh();
        return true;
    } catch {
        return false;
    }
}

async function onEdit(row: any) {
    const res = await getInsiderInfo(row.id)
    formModalApi.setData(res).open();
}

function onDelete(row: any) {
    const hideLoading = message.loading({
        content: $t('ui.actionMessage.deleting', [row.name]),
        duration: 0,
        key: 'action_process_msg',
    });
    deleteInsider(row.id)
        .then(() => {
            message.success({
                content: $t('ui.actionMessage.deleteSuccess', [row.name]),
                key: 'action_process_msg',
            });
            onRefresh();
        })
        .catch(() => {
            hideLoading();
        });
}

function onRefresh() {
    gridApi.query();
}

function onCreate() {
    formModalApi.setData({}).open();
}
</script>
<template>
    <Page auto-content-height>
        <FormModal @success="onRefresh" />
        <Grid>
            <template #toolbar-actions>
                <Button type="primary" @click="onCreate">
                    <Plus class="size-5" />
                    新增内部人员
                </Button>
            </template>
            <template #roleRender="{ row }">
                <div class="flex items-center justify-center gap-1 flex-wrap">
                    <Tag color="default" v-for="tag in row.roles">{{ tag.name }}</Tag>
                </div>
            </template>
        </Grid>
    </Page>
</template>
