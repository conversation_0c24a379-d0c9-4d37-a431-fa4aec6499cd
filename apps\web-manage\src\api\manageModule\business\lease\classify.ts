import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getGoodsTypetList(params: Recordable<any>) {
  return requestClient.get('/biz/rentalGoodsType/list', { params });
}
async function getAllGoodsTypetList(params: Recordable<any>) {
  return requestClient.get('/biz/rentalGoodsType/all', { params });
}

async function getGoodsTypeInfo(id: string) {
  return requestClient.get('/biz/rentalGoodsType/info', { params: { id } });
}

async function createGoodsType(data: Recordable<any>) {
  return requestClient.post('/biz/rentalGoodsType/create', data);
}
async function updateGoodsType(data: Recordable<any>) {
  return requestClient.post('/biz/rentalGoodsType/update', data);
}
async function deleteGoodsType(data: Recordable<any>) {
  return requestClient.post('/biz/rentalGoodsType/delete', data);
}

export {
  getGoodsTypetList,
  getAllGoodsTypetList,
  getGoodsTypeInfo,
  createGoodsType,
  updateGoodsType,
  deleteGoodsType,
};
