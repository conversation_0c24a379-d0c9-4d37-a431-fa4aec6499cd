import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';

const { accessAllEnums } = toRefs(useAccessStore());

export function useCustomerStatus(row: any) {
  return accessAllEnums.value.customerStatus.list.find(
    (item: any) => item.value == row.customerStatus,
  )?.label;
}

export function customerStatusColor(row: any) {
  switch (row.customerStatus) {
    case 1:
      return 'processing';
    case 2:
      return 'success';
    case 3:
      return 'warning';
    default:
      return 'error';
  }
}

// 搜索表单
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '客户名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入客户名称',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '联系电话',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入联系电话',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'principal',
      label: '销售负责人',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入销售负责人',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '客户状态',
      hideLabel: true,
      componentProps: {
        options: accessAllEnums.value.customerStatus.list,
        optionType: 'default',
        placeholder: '请选择客户状态',
        allowClear: true,
      },
    },
  ];
}

// 表格列
export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'serialNumber',
      title: '客户编码',
      fixed: 'left',
    },
    {
      field: 'customerName',
      title: '客户名称',
      fixed: 'left',
    },
    {
      field: 'customerStatus',
      title: '客户状态',
      slots: {
        default: 'customerStatus',
      },
    },
    {
      field: 'region',
      title: '地区',
    },
    {
      field: 'address',
      title: '详细地址',
    },
    {
      field: 'source',
      title: '客户来源',
    },
    {
      field: 'contactName',
      title: '联系人',
    },
    {
      field: 'contactPhone',
      title: '联系电话',
    },
    {
      field: 'principal',
      title: '销售负责人',
    },
    {
      field: 'tags',
      title: '标签',
      width: 'auto',
      slots: {
        default: 'tableTags',
      },
    },
    {
      field: 'remark',
      title: '备注',
    },
    {
      field: 'createdAt',
      title: '创建时间',
    },
    
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'customerName',
          nameTitle: '客户',
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 130,
    },
  ];
}
