import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
import { z } from '#/adapter/form';
import dayjs from 'dayjs';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '企业名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入企业名称',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '企业状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择企业状态',
        allowClear: true,
        options: [
          { label: '正常', value: 1 },
          { label: '锁定', value: -1 },
        ],
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'tenantName',
      title: '企业名称',
      width: 200,
    },
    {
      field: 'tenantCode',
      title: '企业编码',
      width: 150,
    },
    {
      field: 'tenantLogo',
      title: '企业logo',
      width: 150,
      cellRender: {
        name: 'CellImage',
        attrs: {
          height: 60,
          width: 60,
        },
      },
    },
    {
      field: 'contacts',
      title: '联系人',
      width: 120,
    },
    {
      field: 'phone',
      title: '联系电话',
      width: 150,
    },
    {
      field: 'email',
      title: '联系邮箱',
      width: 180,
    },
    {
      field: 'expired',
      title: '过期时间',
      width: 150,
    },
    {
      field: 'status',
      title: '状态',
      width: 150,
      cellRender: {
        name: 'CellTag',
        options: [
          { label: '正常', value: 1, color: 'success' },
          { label: '锁定', value: -1, color: 'warning' },
        ],
      },
    },
    {
      field: 'remark',
      title: '备注',
      minWidth: 150,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'tenantName',
          nameTitle: '企业',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          { code: 'permission', text: '权限设置' },
          { code: 'reset', text: '初始化' },
          'edit',
          'delete',
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 180,
    },
  ];
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'tenantName',
      label: '企业名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入企业名称',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'tenantCode',
      label: '企业编码',
      rules: 'required',
      componentProps: {
        placeholder: '请输入企业编码',
        allowClear: true,
      },
    },
    {
      component: 'CusUpload',
      fieldName: 'tenantLogo',
      label: 'logo',
      componentProps: {
        maxCount: 1,
        accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
        fileTypeTag: 'tenant',
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
      },
      rules: z
        .array(z.object({ url: z.string().url() }))
        .min(1, '请上传企业logo'),
    },
    {
      component: 'Input',
      fieldName: 'contacts',
      label: '联系人',
      rules: 'required',
      componentProps: {
        placeholder: '请输入联系人',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '联系电话',
      rules: z.string().regex(/^1[3456789]\d{9}$/, '请输入正确的手机号码'),
      componentProps: {
        placeholder: '请输入联系电话',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'email',
      label: '邮箱',
      rules: z.string().email('请输入正确的邮箱'),
      componentProps: {
        placeholder: '请输入邮箱',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'status',
      label: '企业状态',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择企业状态',
        allowClear: true,
        options: [
          { label: '正常', value: 1 },
          { label: '锁定', value: -1 },
        ],
      },
      defaultValue: 1,
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        allowClear: true,
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'expired',
      label: '过期时间',
      help: '不选择则永久有效',
      componentProps: {
        placeholder: '请选择过期时间',
        allowClear: true,
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        // 不能选择今天之前的日期
        disabledDate: (current: any) => {
          return current && current < dayjs().endOf('day');
        },
      },
      defaultValue: null,
    },
  ];
}

export function usePermissionFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'RadioGroup',
      fieldName: 'module',
      label: '权限模块',
      componentProps: {
        buttonStyle: 'solid',
        optionType: 'button',
        options: [
          { label: '管理端', value: 'manage' },
          { label: '售票端', value: 'window' },
        ],
      },
      defaultValue: 'manage',
    },
    {
      component: 'Input',
      fieldName: 'permissionIds',
      formItemClass: 'items-start',
      label: '权限',
      modelPropName: 'modelValue',
    },
  ];
}
