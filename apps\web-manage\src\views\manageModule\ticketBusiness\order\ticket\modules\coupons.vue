<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { Table } from 'ant-design-vue';
import { useVbenModal } from '@vben/common-ui';
import { useCouponTableSchema } from '../data';
import { getOrderCouponInfo } from '#/api/manageModule';

const [Modal, modalApi] = useVbenModal({
  showCancelButton: false,
  async onConfirm() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    const data = modalApi.getData<any>();
    if (isOpen) {
      params.value.orderId = data.orderId;
      getOrderCouponList();
    }
  },
});

const orderLog = ref([]);
const params = ref({
  orderId: '',
});
const getOrderCouponList = async () => {
  const res = await getOrderCouponInfo(params.value);
  orderLog.value = res
};
</script>
<template>
  <Modal class="w-[1000px]" title="优惠券使用记录">
    <Table
      :columns="useCouponTableSchema()"
      :dataSource="orderLog"
      :pagination="false"
    >
    <template #bodyCell="{ column, text, record }">
      <template v-if="column.dataIndex === 'thresholdType'">
        {{ record.thresholdType == 1 ? '无门槛' : `最低消费${record.thresholdPrice}元` }}
      </template>
    </template>
    </Table>
  </Modal>
</template>
