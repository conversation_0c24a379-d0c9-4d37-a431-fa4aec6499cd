import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getGoodsList(params: Recordable<any>) {
  return requestClient.get('/biz/rentalGoods/list', { params });
}
async function getAllGoodsList(params: Recordable<any>) {
  return requestClient.get('/biz/rentalGoods/all', { params });
}

async function getGoodsInfo(id: string) {
  return requestClient.get('/biz/rentalGoods/info', { params: { id } });
}

async function createGoods(data: Recordable<any>) {
  return requestClient.post('/biz/rentalGoods/create', data);
}
async function updateGoods(data: Recordable<any>) {
  return requestClient.post('/biz/rentalGoods/update', data);
}
async function deleteGoods(data: Recordable<any>) {
  return requestClient.post('/biz/rentalGoods/delete', data);
}
async function changeGoodsStatus(data: Recordable<any>) {
  return requestClient.post('/biz/rentalGoods/changeStatus', data);
}

export {
  getGoodsList,
  getAllGoodsList,
  getGoodsInfo,
  createGoods,
  updateGoods,
  deleteGoods,
  changeGoodsStatus,
};
