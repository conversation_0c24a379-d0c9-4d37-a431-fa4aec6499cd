<script setup lang="ts">
import { ref, onMounted, toRefs } from 'vue';
import { Page, useVbenModal } from '@vben/common-ui';
import {
  PageHeader,
  Card,
  Button,
  Descriptions,
  DescriptionsItem,
  Table,
} from 'ant-design-vue';
import {
  getCustomizeOrderInfo,
  getCustomizeOrderLog,
} from '#/api/manageModule';
import { useRoute } from 'vue-router';
import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());

import { useLogTableSchema } from './data';
import Refund from './modules/refund.vue';

const route = useRoute();
const orderId = ref<any>(route.query.id);
const orderInfo = ref<any>({});
const getOrderInfoData = async () => {
  const res = await getCustomizeOrderInfo(orderId.value);
  orderInfo.value = res;
};
const orderLog = ref([]);
const total = ref(0);
const params = ref({
  orderId: orderId.value,
  page: 1,
  pageSize: 10,
});
const getCustomizeOrderLogList = async () => {
  const res = await getCustomizeOrderLog(params.value);
  orderLog.value = res.list;
  total.value = res.total;
};
onMounted(() => {
  getOrderInfoData();
  getCustomizeOrderLogList();
});

const resetData = () => {
  getOrderInfoData();
  getCustomizeOrderLogList();
};

// 退款弹窗
const [RefundModal, refundModalApi] = useVbenModal({
  connectedComponent: Refund,
  destroyOnClose: true,
});

const orderRefund = () => {
  refundModalApi.setData(orderInfo.value).open();
};
const filterText = (arr: any[], val: any) => {
  return arr.find((item: any) => item.value === val)?.label;
};
</script>
<template>
  <Page>
    <div class="bg-card">
      <PageHeader
        title="自定义收款单详情"
        class="p-3"
        @back="() => $router.back()"
      ></PageHeader>
      <div class="px-3 pb-3">
        <Card title="订单信息" class="mb-5">
          <template #extra>
            <Button @click="orderRefund" v-if="orderInfo.orderStatus == 2"
              >退款</Button
            >
          </template>
          <Descriptions>
            <DescriptionsItem label="订单号">{{
              orderInfo.orderNo
            }}</DescriptionsItem>
            <DescriptionsItem label="订单名称">{{
              orderInfo.orderName
            }}</DescriptionsItem>
            <DescriptionsItem label="下单时间">{{
              orderInfo.orderTime
            }}</DescriptionsItem>
            <DescriptionsItem label="订单状态">{{
              filterText(
                accessAllEnums?.orderStatus.list,
                orderInfo.orderStatus,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="支付状态">{{
              filterText(
                accessAllEnums?.orderPayStatus.list,
                orderInfo.payStatus,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="支付方式">{{
              filterText(
                accessAllEnums?.orderPayMethod.list,
                orderInfo.payMethod,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="订单来源">{{
              filterText(
                accessAllEnums?.ticketOrderSource.list,
                orderInfo.orderSource,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="联系人">
              {{ orderInfo.userInfo?.name }}
              <span class="ml-2" v-if="orderInfo.userInfo?.phone">{{
                orderInfo.userInfo?.phone
              }}</span>
            </DescriptionsItem>
            <DescriptionsItem label="订单金额">
              {{ orderInfo.orderPrice }}
            </DescriptionsItem>
            <DescriptionsItem label="售票员">
              {{ orderInfo.sellerInfo?.name }}
              <span class="ml-2" v-if="orderInfo.sellerInfo?.phone">{{
                orderInfo.sellerInfo?.phone
              }}</span>
            </DescriptionsItem>
            <DescriptionsItem label="备注">
              {{ orderInfo.remark }}
            </DescriptionsItem>
          </Descriptions>
        </Card>
        <Card title="订单日志">
          <Table
            :columns="useLogTableSchema()"
            :dataSource="orderLog"
            :pagination="{
              current: params.page,
              pageSize: params.pageSize,
              total: total,
              onChange: (page, pageSize) => {
                params.page = page;
                params.pageSize = pageSize;
                getCustomizeOrderLogList();
              },
              showTotal: (total) => `共 ${total} 条`,
            }"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex == 'touristName'">
                <p v-if="record.touristName">
                  {{ record.touristName }}<br />{{ record.touristIdcard }}
                </p>
                <p v-else>--</p>
              </template>
              <template v-if="column.dataIndex == 'ticketName'">
                <p v-if="record.adminUserInfo">
                  {{ record.adminUserInfo?.name }}<br />{{
                    record.adminUserInfo?.phone
                  }}
                </p>
                <p v-else>--</p>
              </template>
            </template>
          </Table>
        </Card>
      </div>
    </div>
    <RefundModal @success="resetData"></RefundModal>
  </Page>
</template>
