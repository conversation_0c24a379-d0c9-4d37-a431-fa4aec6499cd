<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Descriptions, DescriptionsItem, Image } from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

const touristInfo = ref<any>({});
const [Model, modelApi] = useVbenModal({
  title: '游客详情',
  showCancelButton: false,
  confirmText: '关闭',
  async onConfirm() {
    modelApi.close();
  },
  onOpenChange(isOpen) {
    let data = modelApi.getData();
    if (isOpen) {
      touristInfo.value = data;
    }
  },
});

const filterStatus = (val: any) => {
  return accessAllEnums.value?.ticketCardStatus.list.find(
    (item: any) => item.value === val,
  )?.label;
};
</script>
<template>
  <Model class="w-[50%]">
    <Descriptions title="" bordered :column="2">
      <DescriptionsItem label="游客姓名">{{
        touristInfo.name
      }}</DescriptionsItem>
      <DescriptionsItem label="游客性别">{{
        touristInfo.sex === 0 ? '未知' : touristInfo.sex === 1 ? '男' : '女'
      }}</DescriptionsItem>
      <DescriptionsItem label="游客生日">{{
        touristInfo.birthday
      }}</DescriptionsItem>
      <DescriptionsItem label="游客手机号">{{
        touristInfo.phone
      }}</DescriptionsItem>
      <DescriptionsItem label="游客身份证号">{{
        touristInfo.idcard
      }}</DescriptionsItem>
      <DescriptionsItem label="是否默认">{{
        touristInfo.default === 0 ? '否' : '是'
      }}</DescriptionsItem>
      <DescriptionsItem label="创建时间" :span="2">{{
        touristInfo.createdAt
      }}</DescriptionsItem>
      <DescriptionsItem label="所属用户姓名">{{
        touristInfo.userInfo.userName
      }}</DescriptionsItem>
      <DescriptionsItem label="所属用户手机号">{{
        touristInfo.userInfo.phone
      }}</DescriptionsItem>
    </Descriptions>
  </Model>
</template>
