<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { getTouristList, getTouristInfo } from '#/api/manageModule';
import { useColumns, useGridFormSchema } from './data';
import { Button, Modal, message } from 'ant-design-vue';
import Info from './modules/info.vue';

const showInfo = async (row: any) => {
  let info = await getTouristInfo({ id: row.id });
  infoModelApi.setData(info).open();
};
const [InfoModel, infoModelApi] = useVbenModal({
  connectedComponent: Info,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await getTouristList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});
</script>
<template>
  <Page auto-content-height>
    <Grid>
      <template #userName="{ row }">
        <div>
          <p>{{ row.userInfo.userName }}</p>
          <p>（ID：{{ row.userInfo.userId }}）</p>
        </div>
      </template>
      <template #userPhone="{ row }">
        <div>
          {{ row.userInfo.phone }}
        </div>
      </template>
      <template #action="{ row }">
        <Button type="link" size="small" @click="showInfo(row)">详情</Button>
      </template>
    </Grid>
    <InfoModel></InfoModel>
  </Page>
</template>
