<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createUser, updateUser } from '#/api/manageModule';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<any>();

const [Form, formApi] = useVbenForm({
    schema: useFormSchema(),
    showDefaultActions: false,
    // 一行显示1个
    wrapperClass: 'grid-cols-1',
    commonConfig: {
        // 所有表单项
        componentProps: {
            class: 'w-full',
        },
    },
});

const id = ref();
const [Modal, modalApi] = useVbenModal({
    async onConfirm() {
        await handleSubmit();
    },
    onOpenChange(isOpen) {
        if (isOpen) {
            const data = modalApi.getData<any>();
            formApi.resetForm();
            if (data) {
                if (data.id) {
                    formApi.setState({
                        schema: useFormSchema().filter((item: any) => item.fieldName != 'status'),
                    })
                }
                const newFormData = { ...data };
                if (data.avatar) {
                    newFormData.avatar = data.avatar
                        .split(',')
                        .map((item: any) => {
                            return {
                                uid: item,
                                name: item,
                                status: 'done',
                                url: item,
                                response: {
                                    url: item,
                                },
                            };
                        });
                } else {
                    newFormData.avatar = [];
                }
                if (data.roles) {
                    newFormData.roles = data.roles.map((item: any) => {
                        return item.id
                    });
                }
                formData.value = newFormData;
                id.value = data.id;
                formApi.setValues(formData.value);
            } else {
                id.value = undefined;
            }
        }
    },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
    const { valid } = await formApi.validate();
    if (!valid) return null;

    const values = await formApi.getValues();
    values.avatar = values.avatar.map((item: any) => item.url).join(',');

    return values;
};

// 统一的提交处理函数
const handleSubmit = async () => {
    const values = await processFormValues();
    console.log(values, 'submit');
    if (!values) return;
    modalApi.lock();
    try {
        if (id.value) {
            await updateUser({ id: id.value, ...values });
        } else {
            await createUser({ ...values });
        }
        message.success('保存成功');
        emits('success');
        modalApi.close();
    } catch (error) {
        modalApi.unlock();
    }
};

const getModalTitle = computed(() => {
    return formData.value?.id
        ? $t('common.edit', $t('system.role.name'))
        : $t('common.create', $t('system.role.name'));
});
</script>
<template>
    <Modal :title="getModalTitle">
        <Form>
        </Form>
    </Modal>
</template>
