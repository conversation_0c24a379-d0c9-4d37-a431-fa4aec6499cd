<script setup lang="ts">
import { ref } from 'vue';
import { Page, useVbenModal, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { Modal, message, Button } from 'ant-design-vue';
import { Plus } from '@vben/icons';
import { useColumns, useGridFormSchema } from './data';
import {
  getPrintTemplateList,
  getPrintTemplateInfo,
  deletePrintTemplate,
  changePrintTemplateStatus,
} from '#/api/manageModule';

import Form from './modules/form.vue';
import PrintTemplate from './modules/printTemplate/index.vue';

const onActionClick = ({ code, row }: { code: string; row: any }) => {
  if (code === 'edit') {
    onEdit(row);
  } else if (code === 'delete') {
    onDelete(row);
  } else if (code === 'config') {
    onConfig(row);
  }
};

const onCreate = () => {
  formModelApi.setData({}).open();
};

const onEdit = async (row: any) => {
  const res: any = await getPrintTemplateInfo(row.id);
  formModelApi.setData(res).open();
};

const onDelete = async (row: any) => {
  const hideLoading = message.loading({
    content: '删除中...',
    duration: 0,
    key: 'action_process_msg',
  });
  deletePrintTemplate(row.id)
    .then(() => {
      message.success('删除成功');
      onRefresh();
      hideLoading();
    })
    .catch(() => {
      hideLoading();
    });
};

const onConfig = async (row: any) => {
  const res: any = await getPrintTemplateInfo(row.id);
  templateFormDrawerApi.setData(res).open();
};

const onStatusChange = async (newStatus: any, row: any) => {
  const status: any = {
    0: '禁用',
    1: '启用',
  };
  try {
    await confirm(
      `你要将景区【${row.templateName}】的状态切换为 【${status[newStatus.toString()]}】 吗？`,
      `切换状态`,
    );
    await changePrintTemplateStatus({ id: row.id, status: newStatus });
    return true;
  } catch {
    return false;
  }
};
const [FormModel, formModelApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});
const [TemplateFormDrawer, templateFormDrawerApi] = useVbenDrawer({
  connectedComponent: PrintTemplate,
  destroyOnClose: true,
});

const onRefresh = () => {
  gridApi.query();
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: false,
  },

  gridOptions: {
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res: any = await getPrintTemplateList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
const confirm = (content: string, title: string) => {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
};
</script>

<template>
  <Page auto-content-height>
    <FormModel @success="onRefresh" />
    <TemplateFormDrawer @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          新增打印模版
        </Button>
      </template>
    </Grid>
  </Page>
</template>
