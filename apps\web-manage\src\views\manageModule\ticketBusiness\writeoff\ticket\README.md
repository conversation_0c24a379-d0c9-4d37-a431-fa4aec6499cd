# 门票订单表格单元格合并功能

## 功能说明

在门票订单管理页面的展开表格中，实现了单元格合并功能。当一个订单包含多个门票项目时，以下字段会根据 `orderItemList` 的长度进行单元格合并：

### 合并字段
- 下单时间 (orderTime)
- 订单金额 (orderPrice)
- 优惠金额 (discountPrice)
- 实际支付金额 (actualPrice)
- 订单类型 (orderType)
- 支付状态 (payStatus)
- 订单状态 (orderStatus)
- 支付方式 (payMethod)

### 实现原理

1. **数据预处理**: 在获取订单数据后，为每个 `orderItemList` 中的项目添加：
   - `orderId`: 关联的订单ID
   - `itemIndex`: 在订单项目列表中的索引位置

2. **单元格合并逻辑**: 
   - 使用 Ant Design Vue Table 的 `customCell` 属性
   - 对于需要合并的字段，第一行 (`itemIndex === 0`) 显示内容并设置 `rowSpan` 为订单项目总数
   - 其他行设置 `rowSpan: 0` 隐藏单元格

3. **动态合并**: 根据每个订单的 `orderItemList.length` 动态决定合并的行数

### 代码结构

```typescript
// 需要合并的字段列表
const mergeFields = [
  'orderTime', 'orderPrice', 'discountPrice', 'actualPrice',
  'orderType', 'payStatus', 'orderStatus', 'payMethod'
];

// 单元格合并函数
const getMergeProps = (field: string, index: number | undefined, dataSource: any[]) => {
  if (!mergeFields.includes(field) || index === undefined) {
    return {};
  }
  
  if (index === 0) {
    return { rowSpan: dataSource.length };
  }
  
  return { rowSpan: 0 };
};
```

### 使用示例

当一个订单包含3个门票项目时：
- 第一行：显示订单信息，合并字段的 `rowSpan` 为 3
- 第二行：只显示门票信息，合并字段的 `rowSpan` 为 0（隐藏）
- 第三行：只显示门票信息，合并字段的 `rowSpan` 为 0（隐藏）

### 注意事项

1. 确保每个订单项目都有唯一的标识符用于 `row-key`
2. 合并字段的数据在数据预处理阶段已经复制到每个订单项目中
3. 表格需要设置 `:key="row.id"` 确保正确的数据绑定和重新渲染
