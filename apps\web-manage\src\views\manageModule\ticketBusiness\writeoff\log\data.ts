import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'ticketName',
      label: '门票名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入门票名称',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'verificationType',
      label: '核销类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择核销类型',
        allowClear: true,
        options: accessAllEnums.value?.tickeVerificationType.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'verificationMode',
      label: '核销方式',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择核销方式',
        allowClear: true,
        options: accessAllEnums.value?.tickeVerificationMode.list,
      },
    },
    {
      component: 'Input',
      fieldName: 'verificationCode',
      label: '核销码',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入核销码',
        allowClear: true,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'verificationDate',
      label: '核销时间',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['核销开始时间', '核销结束时间'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}
export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '门票名称',
      field: 'ticketName',
      minWidth: 180,
      align: 'center',
    },
    {
      title: '子票名称',
      field: 'childTicketName',
      minWidth: 150,
      align: 'center',
    },
    {
      title: '所属景区',
      field: 'scenicInfo',
      width: 120,
      align: 'center',
      formatter: ({ row }: any) => {
        return row.scenicInfo?.scenicName;
      },
    },
    {
      title: '核销码',
      field: 'verificationCode',
      width: 180,
      align: 'center',
    },
    {
      title: '核销数量',
      field: 'verificationNum',
      width: 120,
      align: 'center',
    },
    {
      title: '核销方式',
      field: 'verificationMode',
      width: 150,
      align: 'center',
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.tickeVerificationMode.list.find(
          (item) => item.value === row.verificationMode,
        )?.label;
      },
    },
    {
      title: '核销类型',
      field: 'verificationType',
      width: 150,
      align: 'center',
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.tickeVerificationType.list.find(
          (item) => item.value === row.verificationType,
        )?.label;
      },
    },
    {
      title: '核销员',
      field: 'adminUserInfo',
      width: 180,
      align: 'center',
      slots: { default: 'adminUserInfo' },
    },
    {
      title: '核销时间',
      field: 'verificationTime',
      width: 180,
      align: 'center',
    },
  ];
}
