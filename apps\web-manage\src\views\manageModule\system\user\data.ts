import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

import { $t } from '#/locales';
import { z } from '#/adapter/form';
import { getAllRoleList } from '#/api/manageModule';
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '姓名',
      rules: 'required',
      componentProps: {
        placeholder: '请输入姓名',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'userName',
      label: '登录用户名',
      rules: 'required',
      componentProps: {
        placeholder: '请输入登录用户名',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '手机号',
      rules: 'required',
      componentProps: {
        placeholder: '请输入登录手机号',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'roles',
      label: '角色权限',
      rules: 'selectRequired',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.name,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllRoleList,
        placeholder: '请选择角色权限',
        allowClear: true,
        mode: 'multiple',
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'CusUpload',
      fieldName: 'avatar',
      label: '头像',
      componentProps: {
        maxCount: 1,
        accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
        fileTypeTag: 'avatar',
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
      },
      rules: z.array(z.object({ url: z.string().url() })).min(1, '请上传头像'),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '保密', value: 0 },
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
      },
      defaultValue: 0,
      fieldName: 'sex',
      label: '性别',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
      defaultValue: 0,
      fieldName: 'isVirtual',
      label: '虚拟用户',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注（最多200字）',
        allowClear: true,
        maxlength: 200,
      },
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '用户名',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入用户名',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: [
          {
            label: '启用',
            value: 1,
          },
          {
            label: '禁用',
            value: -1,
          },
          {
            label: '锁定',
            value: -2,
          },
        ],
      },
    },
    {
      component: 'Select',
      fieldName: 'isVirtual',
      label: '是否虚拟用户',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择是否虚拟用户',
        allowClear: true,
        options: [
          {
            label: '是',
            value: 1,
          },
          {
            label: '否',
            value: 0,
          },
        ],
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'avatar',
      title: '头像',
      width: 120,
      cellRender: {
        attrs: {
          height: 60,
          width: 60,
        },
        name: 'CellAvatar',
      },
    },
    {
      field: 'userName',
      title: '账户名',
      width: 120,
    },
    {
      field: 'name',
      title: '姓名',
      width: 100,
    },
    {
      field: 'sex',
      title: '性别',
      width: 80,
      formatter: ({ row }: any) => {
        return row.sex == 1 ? '男' : row.sex == 2 ? '女' : '保密';
      },
    },
    {
      field: 'phone',
      title: '手机号',
      width: 100,
    },
    {
      field: 'email',
      title: '邮箱',
      width: 120,
    },
    {
      field: 'roles',
      title: '角色',
      width: 200,
      slots: { default: 'roleRender' },
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      formatter: ({ row }: any) => {
        return row.status == 1 ? '启用' : row.status == -1 ? '禁用' : '锁定';
      },
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'success', label: '启用', value: 1 },
          { color: 'error', label: '禁用', value: -1 },
          { color: 'warning', label: '锁定', value: -2 },
        ],
      },
    },
    {
      field: 'isVirtual',
      title: '是否虚拟用户',
      width: 120,
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'success', label: '是', value: 1 },
          { color: 'default', label: '否', value: 0 },
        ],
      },
    },
    {
      field: 'lastLoginTime',
      title: '最后登录时间',
      width: 200,
    },
    {
      field: 'remark',
      minWidth: 100,
      title: $t('system.role.remark'),
    },
    {
      field: 'createdAt',
      title: $t('system.role.createTime'),
      width: 200,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: $t('system.role.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'enable',
            text: '启用',
            show: (row: any) => row.status == -1,
          },
          {
            code: 'disable',
            text: '禁用',
            show: (row: any) => row.status == 1,
            danger: true,
          },
          {
            code: 'unlock',
            text: '解锁',
            show: (row: any) => row.status == -2,
          },
          { code: 'resetPwd', text: '重置密码' },
          'edit',
          'delete',
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: $t('system.role.operation'),
      width: 180,
    },
  ];
}
