<script lang="ts" setup>
import type { Recordable } from '@vben/types';
import { computed, ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { Input, Button, message } from 'ant-design-vue';
import { Plus } from '@vben/icons';
import { z } from '#/adapter/form';

import { getCardInfoByNo, rechargeCard, replaceCard, createCard, amountChange } from '#/api/manageModule';

import Recharge from './recharge/index.vue';

const emit = defineEmits(['confirm']);

const formData = ref<Recordable<any>>({});
const modelTitle = ref('开卡');
const disabled = ref(false);
const type = ref('');
const useFormSchema = (disabled: any) => {
  return [
    {
      component: 'Input',
      label: '卡号',
      fieldName: 'cardNo',
      componentProps: {
        placeholder: '请输入卡号',
        allowClear: true,
        disabled: computed(() => type.value == 'amountChange' || (type.value == 'recharge' && id.value)),
      },
      rules: 'required',
    },
    {
      component: 'Input',
      label: '姓名',
      fieldName: 'name',
      rules: 'required',
      componentProps: {
        placeholder: '请输入姓名',
        disabled,
      },
    },
    {
      component: 'Input',
      label: '手机号',
      fieldName: 'phone',
      rules: 'required',
      componentProps: {
        placeholder: '请输入手机号',
        disabled,
      },
    },
    {
      component: 'InputNumber',
      label: '账户金额',
      fieldName: 'balance',
      componentProps: {
        placeholder: '请输入账户金额',
        disabled,
      },
      suffix: '元',
      dependencies: {
        show: computed(() => type.value !== 'create'),
        triggerFields: ['balance'],
      },
    },
    {
      component: 'InputNumber',
      label: '充值金额',
      fieldName: 'recharge',
      rules: 'required',
      componentProps: {
        placeholder: '请输入充值金额',
      },
      dependencies: {
        show: computed(() => type.value !== 'replace' && type.value !== 'amountChange'),
        triggerFields: ['recharge'],
      },
    },
    {
      component: 'InputNumber',
      label: '变更金额',
      fieldName: 'amount',
      help: '输入正数为增加余额，输入负数为减少余额',
      rules: 'required',
      componentProps: {
        placeholder: '输入正数为增加余额，输入负数为减少余额',
      },
      suffix: '元',
      dependencies: {
        show: computed(() => type.value === 'amountChange'),
        triggerFields: ['amount'],
      },
    },
  ];
};

// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    disabled: false,
    componentProps: {
      class: 'w-full',
    },
  },
  fieldMappingTime: [],
  schema: useFormSchema(computed(() => disabled.value)),
  showDefaultActions: false,
  //一行显示1个
  wrapperClass: 'grid-cols-1',
});

// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    const data = modelApi.getData<any>();
    formApi.resetForm();
    if (isOpen) {
      disabled.value = true;
      type.value = data.type;
      if (data.id) {
        if (data.type == 'replace') {
          modelTitle.value = '补卡';
        } else if (data.type == 'recharge') {
          modelTitle.value = '充值';
        } else {
          modelTitle.value = '余额变更';
        }
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };
        if (type.value == 'replace') {
          newFormData.cardNo = '';
        }
        // 赋值给formData.value
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      } else {

        if (data.type == 'create') {
          modelTitle.value = '开卡';
          disabled.value = false;
          formApi.setState({
            schema: useFormSchema(computed(() => disabled.value)).map(
              (item: any) => {
                if (item.fieldName == 'phone') {
                  item.rules = z
                    .string()
                    .regex(/^1[3456789]\d{9}$/, '请输入正确的手机号');
                }
                return item;
              },
            ),
          });
        } else {
          modelTitle.value = '充值';
          type.value = 'recharge';
        }
      }
    }
  },
});

const showCardInfo = async () => {
  const values = await formApi.getValues();
  if (!values.cardNo) {
    message.warning('请先输入卡号！');
    return;
  }
  const res = await getCardInfoByNo({
    cardNo: values.cardNo,
  });
  formApi.setValues({
    name: res.name,
    phone: res.phone,
    balance: res.balance,
  });
};

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();

  return values;
};
const handleSubmit = async () => {
  const values = await processFormValues();
  console.log(values, 'submit');
  if (!values) return;
  modelApi.lock();
  try {
    if (type.value === 'create') {
      await createCard({ ...values, ...values.recharge });
    } else if (type.value === 'replace') {
      await replaceCard({ ...values, id: id.value });
    } else if (type.value === 'recharge') {
      await rechargeCard({ ...values, ...values.recharge });
    } else if (type.value === 'amountChange') {
      await amountChange({ ...values, id: id.value });
    }
    modelApi.close();
    message.success('操作成功');
    emit('confirm');
  } catch (error) {
    modelApi.unlock();
  }
};
</script>

<template>
  <Model class="w-[600px]" :title="modelTitle">
    <Form>
      <template #cardNo="slotProps">
        <Input v-bind="slotProps" placeholder="请输入卡号" @keydown.enter.prevent="showCardInfo"/>
        <Button type="primary" class="ml-2" @click="showCardInfo"
          v-if="type !== 'create' && type !== 'replace' && !id">查询</Button>
        <Button type="primary" class="ml-2"
          v-if="(id && type == 'replace') || (!id && (type == 'create' || type == 'recharge'))">读卡</Button>
      </template>
      <template #recharge="slotProps">
        <Recharge v-bind="slotProps"></Recharge>
      </template>
    </Form>
  </Model>
</template>
