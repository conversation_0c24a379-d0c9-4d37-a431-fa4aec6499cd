<script lang="ts" setup>
import { InputNumber, Select } from 'ant-design-vue';

const props = defineProps(['disabled']);

const emit = defineEmits(['blur', 'change']);

const modelValue = defineModel<[string, string]>({
  default: () => [undefined, undefined],
});

function onChange() {
  emit('change', modelValue.value);
}
</script>
<template>
  <div class="flex w-full gap-1">
    <InputNumber
      v-model:value="modelValue[0]"
      placeholder="请输入退款手续费"
      allow-clear
      @change="onChange"
      @blur="emit('blur', modelValue)"
      class="flex-1"
      :disabled="disabled"
    />
    <Select
      v-model:value="modelValue[1]"
      class="w-[150px]"
      placeholder="请选择扣费单位"
      allow-clear
      :disabled="disabled"
      :class="{ 'valid-success': !!modelValue[0] }"
      :options="[
        { label: '元', value: 1 },
        { label: '%', value: 2 },
      ]"
      @blur="emit('blur')"
      @change="onChange"
    />
  </div>
</template>
