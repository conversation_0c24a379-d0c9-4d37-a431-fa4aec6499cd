import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getVerificationInfo(params: Recordable<any>) {
  return requestClient.get('/tkt/verification/detail', { params });
}

async function getVerificationLog(params: Recordable<any>) {
  return requestClient.get('/tkt/verification/list', { params });
}
async function verificationAction(data: Recordable<any>) {
  return requestClient.post('/tkt/verification/action', data);
}

export { getVerificationInfo, getVerificationLog, verificationAction };
