import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getHolidayList(params: Recordable<any>) {
  return requestClient.get('/sys/holiday/list', { params });
}

async function getHolidayAllList(params: Recordable<any>) {
  return requestClient.get('/sys/holiday/all', { params });
}

async function getHolidayInfo(id: string) {
  return requestClient.get('/sys/holiday/info', { params: { id } });
}

async function createHoliday(data: Recordable<any>) {
  return requestClient.post('/sys/holiday/create', data);
}

async function updateHoliday(data: Recordable<any>) {
  return requestClient.post('/sys/holiday/update', data);
}

async function deleteHoliday(id: string) {
  return requestClient.post('/sys/holiday/delete', { id });
}

export {
  getHolidayList,
  getHolidayAllList,
  getHolidayInfo,
  createHoliday,
  updateHoliday,
  deleteHoliday,
};
