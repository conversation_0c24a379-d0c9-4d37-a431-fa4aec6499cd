<script lang="ts" setup>
import { RadioGroup, RangePicker } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
const emit = defineEmits(['blur', 'change']);
const modelValue = defineModel<[number, [Dayjs, Dayjs]]>({
    default: () => [1, []],
});

function onChange() {
    emit('change', modelValue.value);
}
</script>
<template>
    <div class="flex h-[32px] w-full items-center gap-1">
        <div class="w-2/5">
            <RadioGroup v-model:value="modelValue[0]" :options="[
                { label: '不限', value: 1 },
                { label: '固定日期', value: 2 },
            ]" @change="onChange" @blur="emit('blur', modelValue)" class="w-full" />
        </div>
        <div class="flex flex-1 items-center" v-if="modelValue[0] === 2">
            <div class="mr-2 w-[120px] text-right text-sm font-[500] leading-6">
                可领取日期
            </div>
            <RangePicker v-model:value="modelValue[1]" format="YYYY-MM-DD" :placeholder="['开始日期', '结束日期']" separator="至"
                valueFormat="YYYY-MM-DD" allow-clear @change="onChange" @blur="emit('blur', modelValue)"
                class="w-full" />
        </div>
    </div>
</template>
