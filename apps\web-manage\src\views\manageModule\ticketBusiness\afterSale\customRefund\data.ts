import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

import type { TableColumnType } from 'ant-design-vue';

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'orderNo',
      label: '订单号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入订单号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'refundNo',
      label: '退款编号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入退款编号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'refundStatus',
      label: '退款状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择退款状态',
        allowClear: true,
        options: accessAllEnums.value?.orderRefundStatus.list,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'applyDate',
      label: '申请日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['申请开始日期', '申请结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'orderNo',
      title: '订单号',
      minWidth: 150,
      fixed: 'left',
    },
    {
      field: 'refundNo',
      title: '退款单号',
      minWidth: 150,
      fixed: 'left',
    },
    {
      field: 'refundNum',
      title: '退款数量',
      minWidth: 120,
    },
    {
      field: 'refundPrice',
      title: '退款金额',
      width: 150,
    },
    {
      field: 'refundFeePrice',
      title: '退款手续费',
      width: 150,
    },
    {
      field: 'refundReason',
      title: '退款原因',
      width: 150,
    },
    {
      field: 'applyTime',
      title: '申请时间',
      width: 180,
    },
    {
      field: 'refundTime',
      title: '退款时间',
      width: 180,
    },
    {
      field: 'refundStatus',
      title: '退款状态',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.orderRefundStatus.list.find(
          (item: any) => item.value === row.refundStatus,
        )?.label;
      },
    },
    {
      field: 'adminUserInfo',
      title: '操作员',
      width: 150,
      slots: { default: 'adminUserInfo' },
    },
    {
      field: 'operation',
      title: '操作',
      width: 120,
      slots: { default: 'operation' },
      align: 'center',
      fixed: 'right',
    },
  ];
}

export function useLogTableSchema(): TableColumnType[] {
  return [
    {
      title: '操作内容',
      dataIndex: 'actionContent',
      minWidth: 150,
      align: 'center',
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      width: 180,
      customRender: ({ record }: any) => {
        return accessAllEnums.value?.orderStatus.list.find(
          (item: any) => item.value === record.orderStatus,
        )?.label;
      },
      align: 'center',
    },
    {
      title: '操作管理员',
      dataIndex: 'ticketName',
      width: 180,
      align: 'center',
    },
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      width: 180,
      align: 'center',
    },
  ];
}

export function useRefundFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'InputNumber',
      fieldName: 'refundPrice',
      label: '退款金额',
      rules: 'required',
      componentProps: {
        placeholder: '请输入退款金额',
        allowClear: true,
      },
    },
    {
      component: 'Textarea',
      fieldName: 'refundReason',
      label: '退款原因',
      componentProps: {
        placeholder: '请输入退款原因',
        allowClear: true,
        rows: 3,
      },
    },
  ];
}
