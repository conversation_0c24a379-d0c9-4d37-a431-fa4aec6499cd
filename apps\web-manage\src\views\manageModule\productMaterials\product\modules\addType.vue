<template>
  <Model class="h-[400px]" :title="getTitle">
    <TypeForm class="mx-4" />
  </Model>
</template>
<script lang="ts" setup>
import type { Recordable } from '@vben/types';
import { useVbenModal } from '@vben/common-ui';
import { computed, ref, h } from 'vue';
import { useVbenForm } from '@vben/common-ui';
import type { VbenFormSchema } from '#/adapter/form';
import { getPopupContainer } from '@vben/utils';
import {
  allType,
  createType,
  updateType,
} from '#/api/productMaterials/materials';
const emit = defineEmits(['success']);
const formData = ref<any>();
const getTitle = computed(() => {
  return formData.value?.id ? '编辑' : '新增';
});
// 新增/编辑类型表单
const useAddSchema: VbenFormSchema[] = [
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: allType,
      params: {
        mode: 1,
      },
      class: 'w-full',
      listHeight: 180,
      filterTreeNode(input: string, node: Recordable<any>) {
        if (!input || input.length === 0) {
          return true;
        }
        const title: string = node.typeName ?? '';
        if (!title) return false;
        return title.includes(input);
      },
      getPopupContainer,
      labelField: 'typeName',
      showSearch: true,
      treeDefaultExpandAll: true,
      valueField: 'id',
      childrenField: 'children',
      placeholder: '请选择',
      allowClear: true,
    },
    fieldName: 'parentId',
    label: '上级',
    renderComponentContent() {
      return {
        title({ label, meta }: { label: string; meta: Recordable<any> }) {
          const coms = [];
          if (!label) return '';
          coms.push(h('span', { class: '' }, label || ''));
          return h('div', { class: 'flex items-center gap-1' }, coms);
        },
      };
    },
  },
  {
    component: 'Input',
    fieldName: 'typeName',
    label: '类型名称',
    rules: 'required',
  },
];
const [TypeForm, typeFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useAddSchema,
  showDefaultActions: false,
});
const [Model, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await typeFormApi.validate();
    if (valid) {
      modalApi.lock();
      const data = await typeFormApi.getValues();
      data.parentId = data.parentId ?? 0;
      try {
        await (formData.value?.id
          ? updateType({ id: formData.value.id, mode: 1, ...data })
          : createType({ mode: 1, ...data }));
        modalApi.close();
        emit('success');
      } finally {
        modalApi.lock(false);
      }
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<any>();
      if (data) {
        if (data.parentId === 0) {
          data.parentId = undefined;
        }
        formData.value = data;
        typeFormApi.setValues(formData.value);
      }
    }
  },
});
</script>
