<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useRefundFormSchema } from '../data';
import { customizeOrderRefund } from '#/api/manageModule';
import { message } from 'ant-design-vue';

const emits = defineEmits(['success']);
const title = ref('收款单退款');
const orderData = ref<any>({});
const handleSubmint = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();
  console.log(values);
  handleRefund(values);
};

// 全退
const handleRefund = async (values: any) => {
  const res = await customizeOrderRefund({
    orderId: orderData.value.id,
    ...values,
  });
  resetData();
};

const resetData = () => {
  message.success('操作成功');
  emits('success');
  modalApi.close();
};

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    await handleSubmint();
  },
  onOpenChange(isOpen) {
    const data = modalApi.getData<any>();
    if (isOpen) {
      orderData.value = { ...data };
      console.log('data', orderData.value);
    }
  },
});

// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    labelWidth: 100,
    componentProps: {
      class: 'w-full',
    },
  },
  fieldMappingTime: [],
  scrollToFirstError: true,
  schema: useRefundFormSchema(),
  showDefaultActions: false,
  // 一行显示1个
  wrapperClass: 'grid-cols-1',
});
</script>
<template>
  <Modal :title="title">
    <Form></Form>
  </Modal>
</template>
