import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { getAllScenicList } from '#/api/manageModule';
import { $t } from '#/locales';
import { z } from '#/adapter/form';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '姓名',
      rules: 'required',
      componentProps: {
        placeholder: '请输入姓名',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'insiderGroupType',
      label: '人员类型',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择人员类型',
        allowClear: true,
        options: accessAllEnums.value?.insiderGroupType.list,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '手机号',
      rules: 'required',
      componentProps: {
        placeholder: '请输入手机号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'idcard',
      label: '身份证号',
      rules: 'required',
      componentProps: {
        placeholder: '请输入身份证号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'address',
      label: '地址',
      rules: 'required',
      componentProps: {
        placeholder: '请输入地址',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
      defaultValue: 1,
    },
    {
      component: 'CusUpload',
      fieldName: 'headImg',
      label: '头像',
      componentProps: {
        maxCount: 1,
        accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
        fileTypeTag: 'avatar',
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
      },
      rules: z.array(z.object({ url: z.string().url() })).min(1, '请上传头像'),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '保密', value: 0 },
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
      },
      defaultValue: 0,
      fieldName: 'sex',
      label: '性别',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注（最多200字）',
        allowClear: true,
        maxlength: 200,
      },
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '姓名',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入姓名',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '手机号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入手机号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: [
          {
            label: '启用',
            value: 1,
          },
          {
            label: '禁用',
            value: 0,
          },
        ],
      },
    },
    {
      component: 'Select',
      fieldName: 'insiderGroupType',
      label: '人员类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择人员类型',
        allowClear: true,
        options: accessAllEnums.value?.insiderGroupType.list,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'headImg',
      title: '头像',
      width: 120,
      cellRender: {
        attrs: {
          height: 60,
          width: 60,
        },
        name: 'CellAvatar',
      },
    },
    {
      field: 'name',
      title: '姓名',
      width: 100,
    },
    {
      field: 'insiderGroupType',
      title: '人员类型',
      width: 100,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.insiderGroupType.list.find(
          (item: any) => item.value == row.insiderGroupType,
        )?.label;
      },
    },
    {
      field: 'sex',
      title: '性别',
      width: 80,
      formatter: ({ row }: any) => {
        return row.sex == 1 ? '男' : row.sex == 2 ? '女' : '保密';
      },
    },
    {
      field: 'scenicName',
      title: '所属景区',
      width: 100,
      formatter: ({ row }: any) => {
        return row.scenicInfo.scenicName;
      },
    },
    {
      field: 'phone',
      title: '手机号',
      width: 100,
    },
    {
      field: 'idcard',
      title: '身份证',
      width: 150,
    },
    {
      field: 'address',
      title: '地址',
      minWidth: 150,
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: onStatusChange,
        },
      },
    },
    {
      field: 'createdBy',
      title: '创建人',
      width: 150,
    },
    {
      field: 'remark',
      minWidth: 100,
      title: $t('system.role.remark'),
    },
    {
      field: 'createdAt',
      title: $t('system.role.createTime'),
      width: 200,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: $t('system.role.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('system.role.operation'),
      width: 120,
    },
  ];
}
