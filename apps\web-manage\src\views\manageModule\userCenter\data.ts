import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { z } from '#/adapter/form';
import { useAccessStore } from '@vben/stores';
import dayjs from 'dayjs';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useInfoFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'CusUpload',
      fieldName: 'avatar',
      label: '头像',
      componentProps: {
        maxCount: 1,
        accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
        fileTypeTag: 'avatar',
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
      },
      rules: z.array(z.object({ url: z.string().url() })).min(1, '请上传头像'),
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '姓名',
      rules: 'required',
      componentProps: {
        placeholder: '请输入姓名',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '手机号',
      rules: 'required',
      componentProps: {
        placeholder: '请输入手机号',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'sex',
      label: '性别',
      rules: 'required',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '保密', value: 0 },
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
      },
      defaultValue: 0,
    },
  ];
}

export function usePasswordFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'VbenInputPassword',
      fieldName: 'password',
      label: '旧密码',
      rules: 'required',
      componentProps: {
        placeholder: '请输入旧密码',
        allowClear: true,
      },
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        passwordStrength: true,
        placeholder: '请输入新密码',
      },
      fieldName: 'newPassword',
      label: '新密码',
      renderComponentContent() {
        return {
          strengthText: () => '至少6位字符，支持数字，字母，符号',
        };
      },
      rules: z.string().min(6, { message: '新密码至少6位字符' }),
    },
    {
      component: 'VbenInputPassword',
      fieldName: 'rePassword',
      label: '确认密码',
      rules: 'required',
      componentProps: {
        placeholder: '请确认新密码',
        allowClear: true,
        visible: true,
      },
      dependencies: {
        rules(values) {
          const { newPassword } = values;
          return z
            .string({ required_error: '请确认新密码' })
            .min(1, { message: '请确认新密码' })
            .refine((value) => value === newPassword, {
              message: '两次输入的密码不一致',
            });
        },
        triggerFields: ['newPassword'],
      },
    },
  ];
}

export function loginGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'ip',
      label: '登录IP',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入登录IP',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'type',
      label: '类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择类型',
        allowClear: true,
        options: [
          {
            label: '手机',
            value: 1,
          },
          {
            label: 'PC',
            value: 2,
          },
        ],
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: [
          {
            label: '成功',
            value: 1,
          },
          {
            label: '失败',
            value: 2,
          },
        ],
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'date',
      label: '日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['开始日期', '结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
      defaultValue: [dayjs().startOf('month'), dayjs().endOf('month')], // 默认当月
    },
  ];
}

export function loginColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '登录IP',
      field: 'ip',
      width: 120,
    },
    {
      title: 'IP位置',
      field: 'region',
      width: 200,
    },
    {
      title: 'userAgent信息',
      field: 'userAgent',
      minWidth: 200,
      slots: { default: 'userAgent' },
      align: 'left',
    },
    {
      title: '登录方式',
      field: 'loginType',
      width: 120,
      formatter: ({ row }: any) => {
        return row.loginType === 1 ? '密码登录' : '短信验证码登录';
      },
    },
    {
      title: '登录客户端',
      field: 'client',
      width: 120,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.loginClient.list.find(
          (item: any) => item.value === row.client,
        )?.label;
      },
    },
    {
      title: '状态',
      field: 'status',
      width: 120,
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'success', label: '成功', value: 1 },
          { color: 'error', label: '失败', value: 2 },
        ],
      },
    },
    {
      title: '登录时间',
      field: 'createdAt',
      width: 150,
    },
  ];
}

export function actionGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'title',
      label: '操作名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入操作名称',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: [
          {
            label: '成功',
            value: 1,
          },
          {
            label: '失败',
            value: 2,
          },
        ],
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'date',
      label: '日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['开始日期', '结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
      defaultValue: [dayjs().startOf('month'), dayjs().endOf('month')], // 默认当月
    },
  ];
}

export function actionColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'title',
      title: '操作名称',
      width: 120,
    },
    {
      field: 'module',
      title: '操作模块',
      width: 120,
    },
    {
      title: '登录IP',
      field: 'ip',
      width: 120,
    },
    {
      title: '状态码',
      field: 'code',
      width: 120,
    },
    {
      title: '请求时间',
      field: 'requestTime',
      width: 150,
    },
    {
      title: '提示消息',
      field: 'message',
      minWidth: 120,
    },
    {
      title: '响应时间',
      field: 'responseTime',
      width: 150,
    },
    {
      title: '耗时',
      field: 'duration',
      width: 120,
    },
    {
      title: '请求地址',
      field: 'uri',
      width: 120,
    },
    {
      title: '请求方式',
      field: 'method',
      width: 120,
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'success', label: '成功', value: 1 },
          { color: 'error', label: '失败', value: 2 },
        ],
      },
    },
    {
      field: 'operation',
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: '操作',
      width: 120,
    },
  ];
}
