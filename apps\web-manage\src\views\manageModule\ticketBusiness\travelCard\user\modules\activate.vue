<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { message } from 'ant-design-vue';
import { computed, ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useFormSchema } from '../data';
import { userCardActivation } from '#/api/manageModule';
import CusUpload from '#/components/CusUpload/index.vue';
import { IconifyIcon } from '@vben/icons';

const emits = defineEmits(['success']);

const formData = ref<Recordable<any>>({});
// 表单配置
const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1',
  commonConfig: {
    labelWidth: 120,
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
});

// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    const data = modelApi.getData<any>();
    formApi.resetForm();
    if (isOpen) {
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };
        // 赋值给formData.value
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      }
    }
  },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();
  values.faceImg = values.faceImg.map((item: any) => item.url).join(',');
  return values;
};

// 提交表单
const handleSubmit = async () => {
  const values = await processFormValues();
  if (!values) return;
  modelApi.lock();
  try {
    await userCardActivation({ cardId: id.value, ...values });
    message.success('激活成功');
    emits('success');
    modelApi.close();
  } catch (error) {
    modelApi.unlock();
  }
};
</script>

<template>
  <Model class="w-[500px]" title="激活">
    <Form>
      <template #faceImg="slotProps">
        <div>
          <CusUpload v-bind="slotProps" list-type="picture-card">
            <IconifyIcon icon="mdi:upload" class="size-4" />
          </CusUpload>
          <p class="mt-2 text-[12px] leading-4 text-gray-500">
            <IconifyIcon
              icon="mdi:info"
              class="mr-1 inline-block size-4"
            />建议大于200*200像素，小于1920 *
            1920像素，包含清晰的脸部图片，避免出现模糊、旋转、遮挡、剪裁等情况。以免识别失败！
          </p>
        </div>
      </template>
    </Form>
  </Model>
</template>
