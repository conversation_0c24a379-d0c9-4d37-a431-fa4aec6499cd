import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs, markRaw } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '姓名',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入姓名',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '手机号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入手机号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'idCard',
      label: '身份证号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入身份证号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        options: [
          { label: '正常', value: 1 },
          { label: '黑名单', value: -2 },
        ],
        placeholder: '请选择状态',
        allowClear: true,
      },
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'avatar',
      title: '头像',
      cellRender: {
        attrs: {
          height: 60,
          width: 60,
        },
        name: 'CellAvatar',
      },
      width: 120,
    },
    {
      field: 'nickname',
      title: '昵称',
      width: 150,
    },
    {
      field: 'name',
      title: '姓名',
      width: 120,
    },
    {
      field: 'phone',
      title: '手机号',
      width: 120,
    },
    {
      field: 'idcard',
      title: '身份证号',
      width: 150,
    },
    {
      field: 'birthday',
      title: '生日',
      width: 100,
    },
    {
      field: 'sex',
      title: '性别',
      width: 80,
      formatter: ({ row }: any) => {
        return row.sex == 1 ? '男' : '女';
      },
    },
    {
      field: 'region',
      title: '地区',
      minWidth: 200,
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellTag',
        options: [
          { label: '正常', value: 1, color: 'success' },
          { label: '黑名单', value: 2, color: 'error' },
        ],
      },
      formatter: ({ row }: any) => {
        return [
          { label: '正常', value: 1 },
          { label: '黑名单', value: 2 },
        ].find((item: any) => item.value === row.status)?.label;
      },
    },
    {
      field: 'registerTime',
      title: '注册时间',
      width: 150,
    },
    {
      field: 'lastLoginTime',
      title: '最后登录时间',
      width: 150,
    },
    {
      field: 'lastLoginIp',
      title: '最后登录IP',
      width: 120,
    },
    {
      field: 'operation',
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: '操作',
      width: 180,
    },
  ];
}
