<script setup lang="ts">
import type {
    OnActionClickParams,
    VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';
import { useColumns, useGridFormSchema } from './data';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getLoginLogList } from '#/api/manageModule';
import { RangePicker } from 'ant-design-vue';
import dayjs from 'dayjs';
import { ref } from 'vue';

const [Grid, gridApi] = useVbenVxeGrid({
    formOptions: {
        fieldMappingTime: [['date', ['beginDate', 'endDate']]],
        schema: useGridFormSchema(),
        collapsed: true,
        submitOnChange: true,
        submitOnEnter: true,
        collapsedRows: 1,
        wrapperClass: 'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
        showCollapseButton: true,
    },
    gridOptions: {
        columns: useColumns(),
        height: 'auto',
        keepSource: true,
        headerCellConfig: {
            height: 40,
        },
        cellConfig: {
            height: 80,
        },
        proxyConfig: {
            ajax: {
                query: async ({ page }, formValues) => {
                    const res: any = await getLoginLogList({
                        page: page.currentPage,
                        pageSize: page.pageSize,
                        ...formValues,
                    });
                    return {
                        items: res.list,
                        total: res.total,
                    };
                },
            },
        },
        rowConfig: {
            keyField: 'id',
        },

        toolbarConfig: {
            custom: true,
            export: false,
            refresh: { code: 'query' },
            search: true,
            zoom: true,
        },
    } as VxeTableGridOptions<any>,
});

const startDate = ref(null);

const onCalendarChange = (dates: any) => {
    if (dates && dates.length > 0) {
        startDate.value = dates[0];
    } else {
        startDate.value = null;
    }
}

const disabledDate = (current: any) => {
    // 不能选择今天之后的日期
    if (current && current >= dayjs().endOf('day')) {
        return true;
    }

    // 如果已经选择了开始日期，限制结束日期范围
    if (startDate.value && current) {
        const start = dayjs(startDate.value);
        const diffDays = current.diff(start, 'day');

        // 限制结束日期不能超过开始日期后30天
        if (diffDays > 30) {
            return true;
        }

        // 限制结束日期不能超过开始日期前30天
        if (diffDays < -30) {
            return true;
        }
    }

    return false;
}
</script>
<template>
    <Page auto-content-height>
        <Grid>
            <template #form-date="slotProps">
                <RangePicker v-bind="slotProps" :disabledDate="disabledDate" @calendarChange="onCalendarChange" />
            </template>
            <template #userAgent="{ row }">
                <p class="leading-[18px]">浏览器：{{ row.userAgent.ua }}</p>
                <p class="leading-[18px]">系统：{{ row.userAgent.os }}</p>
                <p class="leading-[18px]">设备类型：{{ row.userAgent.device }}</p>
                {{ row.userAgent.originalUserAgent }}
            </template>
        </Grid>
    </Page>
</template>
