<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { computed, ref, toRefs, watch, defineProps, defineEmits } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Input, Select, Button, Table, Form, FormItem } from 'ant-design-vue';
import { getCouponList } from '#/api/manageModule';
import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());

const emits = defineEmits(['change']);
const props = defineProps({
  selectedCoupons: {
    type: Array,
    default: () => [],
  },
});

const [Model, modelApi] = useVbenModal({
  title: '用户列表',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      setTimeout(() => {
        getCouponLists();
      }, 0);
    }
  },
});
const columns = ref<any>([
  {
    title: '优惠券名称',
    dataIndex: 'couponName',
    align: 'center',
  },
  {
    title: '优惠方式',
    dataIndex: 'couponType',
    align: 'center',
  },
  {
    title: '优惠金额/折扣',
    dataIndex: 'couponValue',
    align: 'center',
  },
  {
    title: '有效期',
    dataIndex: 'validType',
    align: 'center',
  },
  {
    title: '使用门槛',
    dataIndex: 'thresholdType',
    align: 'center',
  },
]);
const params = ref({
  name: undefined,
  couponType: undefined,
  page: 1,
  pageSize: 10,
  status: 1
});

const couponList = ref<any[]>([]);
const total = ref(0);
const getCouponLists = async () => {
  const res = await getCouponList(params.value);
  couponList.value = res.list;
  total.value = res.total;
};
const resetForm = () => {
  params.value = {
    name: undefined,
    couponType: undefined,
    page: 1,
    pageSize: 10,
    status: 1
  };
  getCouponLists();
};
const handleSearch = () => {
  params.value.page = 1;
  getCouponLists();
};


const selectedRowKeys = ref<any[]>([]);
const selectedRows = ref<any[]>([]);

watch(
  () => props.selectedCoupons,
  (val) => {
    if (val && val.length > 0) {
      console.log(val, 'selectedCoupons');
      selectedRows.value = val;
      selectedRowKeys.value = val.map((item: any) => item.id);
    } else {
      selectedRows.value = [];
      selectedRowKeys.value = [];
    }
  },
  { immediate: true, deep: true },
);

const rowSelection = computed(() => ({
  checkStrictly: false,
  selectedRowKeys: selectedRowKeys.value.filter(key => couponList.value.some(item => item.id === key)), // 仅显示当前页面的选定按键
  onChange: (
    currentSelectedKeys: (string | number)[],
    currentSelectedRows: any[],
  ) => {
    const newGlobalSelectedKeys = new Set(selectedRowKeys.value);
    const newGlobalSelectedRows = [...selectedRows.value];

    const currentPageRowIds = new Set(couponList.value.map(item => item.id));

    // 1. Handle deselections:
    const deselectedOnCurrentPage = Array.from(newGlobalSelectedKeys).filter(key =>
      currentPageRowIds.has(key) && !currentSelectedKeys.includes(key)
    );
    deselectedOnCurrentPage.forEach(key => {
      newGlobalSelectedKeys.delete(key);
      const index = newGlobalSelectedRows.findIndex(row => row.id === key);
      if (index !== -1) {
        newGlobalSelectedRows.splice(index, 1);
      }
    });

    // 2. Handle selections:
    currentSelectedRows.forEach(row => {
      if (!newGlobalSelectedKeys.has(row.id)) {
        newGlobalSelectedKeys.add(row.id);
        newGlobalSelectedRows.push(row);
      }
    });

    selectedRowKeys.value = Array.from(newGlobalSelectedKeys);
    selectedRows.value = newGlobalSelectedRows;
  },
  onSelect: (record: any, selected: boolean) => {
    if (selected) {
      if (!selectedRows.value.some((item) => item.id === record.id)) {
        selectedRows.value.push(record);
        selectedRowKeys.value.push(record.id);
      }
    } else {
      selectedRows.value = selectedRows.value.filter(
        (item) => item.id !== record.id,
      );
      selectedRowKeys.value = selectedRowKeys.value.filter(
        (key) => key !== record.id,
      );
    }
  },
  onSelectAll: (
    selected: boolean,
    currentSelectedRows: any[],
    changeRows: any[],
  ) => {
    if (selected) {
      changeRows.forEach((row) => {
        if (!selectedRows.value.some((item) => item.id === row.id)) {
          selectedRows.value.push(row);
          selectedRowKeys.value.push(row.id);
        }
      });
    } else {
      const changeRowIds = new Set(changeRows.map((row) => row.id));
      selectedRows.value = selectedRows.value.filter(
        (item) => !changeRowIds.has(item.id),
      );
      selectedRowKeys.value = selectedRowKeys.value.filter(
        (key) => !changeRowIds.has(key),
      );
    }
  },
}));

const handleSubmit = () => {
  emits(
    'change',
    selectedRows.value
  );
  modelApi.close();
};
</script>
<template>
  <Model class="w-[50%]" title="优惠券列表">
    <Form layout="inline" :model="params" class="mb-2">
      <div class="grid grid-cols-3 gap-2">
        <FormItem label="优惠券名称">
          <Input v-model:value="params.name" allowClear placeholder="请输入优惠券名称" />
        </FormItem>
        <FormItem label="优惠券类型">
          <Select v-model:value="params.couponType" allowClear placeholder="请选择优惠券类型" />
        </FormItem>
        <FormItem>
          <Button @click="resetForm()">重置</Button>
          <Button type="primary" class="ml-2" @click="handleSearch()">搜索</Button>
        </FormItem>
      </div>
    </Form>
    <div>
      <Table :rowSelection="rowSelection" :columns="columns" :dataSource="couponList" rowKey="id" :pagination="{
        current: params.page,
        pageSize: params.pageSize,
        total: total,
        onChange: (page, pageSize) => {
          params.page = page;
          params.pageSize = pageSize;
          getCouponLists();
        },
        showTotal: (total) => `共 ${total} 条`,
      }" bordered>
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'couponType'">
            {{ record.couponType == 1 ? '满减券' : '折扣券' }}
          </template>
          <template v-if="column.dataIndex === 'validType'">
            {{ record.validType == 1 ? '永久有效' : record.validType == 2 ?
              `${record.validBeginDate}~${record.validEndDate}` : `领取后${record.validDayNum}天内有效` }}
          </template>
          <template v-if="column.dataIndex === 'thresholdType'">
            {{ record.thresholdType == 1 ? '无门槛' : `最低消费${record.thresholdPrice}元` }}
          </template>
        </template>
      </Table>
    </div>
  </Model>
</template>
