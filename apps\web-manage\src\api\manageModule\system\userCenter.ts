import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getAdminUserInfo(params: Recordable<any>) {
  return requestClient.get('/sys/adminUser/getMyInfo', { params });
}

async function updateAdminUser(data: Recordable<any>) {
  return requestClient.post('/sys/adminUser/updateMyInfo', data);
}

async function updateAdminPassword(data: Recordable<any>) {
  return requestClient.post('/sys/adminUser/updateMyPassword', data);
}

async function getAdminUserLoginLogList(params: Recordable<any>) {
  return requestClient.get('/sys/adminUserLoginLog/getMyList', { params });
}

async function getAdminUserActionLogList(params: Recordable<any>) {
  return requestClient.get('/sys/adminUserActionLog/myList', { params });
}

export {
  getAdminUserInfo,
  updateAdminUser,
  updateAdminPassword,
  getAdminUserLoginLogList,
  getAdminUserActionLogList,
};
