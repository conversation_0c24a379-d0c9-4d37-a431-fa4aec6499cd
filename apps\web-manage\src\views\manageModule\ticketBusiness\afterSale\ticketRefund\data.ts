import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
import type { TableColumnType } from 'ant-design-vue';
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'orderNo',
      label: '订单号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入订单号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'refundNo',
      label: '退款编号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入退款编号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'refundStatus',
      label: '退款状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择退款状态',
        allowClear: true,
        options: accessAllEnums.value?.orderRefundStatus.list,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'applyDate',
      label: '申请时间',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['申请开始日期', '申请结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      type: 'expand',
      fixed: 'left',
      headerAlign: 'center',
      width: 30,
      slots: { content: 'expand_content' },
    },
    {
      field: 'ticketName',
      title: '门票信息',
      minWidth: 300,
      fixed: 'left',
      headerAlign: 'center',
      align: 'left',
      slots: {
        default: 'expand_header',
      },
    },
    {
      field: 'refundNum',
      title: '退票数量',
      width: 120,
    },
    {
      field: 'refundPrice',
      title: '退款金额',
      width: 120,
    },
    {
      field: 'refundFeePrice',
      title: '退款手续费',
      width: 120,
    },
    {
      field: 'refundStatus',
      title: '退款状态',
      width: 120,
      formatter: ({ row }: any) => {
        return accessAllEnums.value.orderRefundStatus.list.find(
          (item: any) => item.value === row.refundStatus,
        )?.label;
      },
    },
    {
      field: 'applyTime',
      title: '申请时间',
      width: 180,
    },
    {
      field: 'operation',
      align: 'center',
      fixed: 'right',
      title: '操作',
      width: 120,
    },
  ];
}

export function useLogTableSchema(): TableColumnType[] {
  return [
    {
      title: '操作内容',
      dataIndex: 'actionContent',
      minWidth: 150,
      align: 'center',
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      width: 150,
      customRender: ({ record }: any) => {
        return accessAllEnums.value?.ticketOrderStatus.list.find(
          (item: any) => item.value === record.orderStatus,
        )?.label;
      },
      align: 'center',
    },
    {
      title: '下单人',
      dataIndex: 'userInfo',
      width: 150,
      customRender: ({ record }: any) => {
        return record.userInfo?.name || '--';
      },
      align: 'center',
    },
    {
      title: '游客名称',
      dataIndex: 'touristName',
      width: 180,
      align: 'center',
    },
    {
      title: '操作管理员',
      dataIndex: 'ticketName',
      width: 150,
      align: 'center',
    },
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      width: 180,
      align: 'center',
    },
  ];
}
