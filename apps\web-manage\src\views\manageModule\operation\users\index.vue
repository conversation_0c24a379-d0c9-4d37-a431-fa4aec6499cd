<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { getUserList, getUserInfo, changeUserStatus } from '#/api/manageModule';
import { useColumns, useGridFormSchema } from './data';
import { Button, Modal, message } from 'ant-design-vue';
import Info from './modules/info.vue';

const showInfo = async (row: any) => {
  let info = await getUserInfo({ id: row.id });
  infoModelApi.setData(info).open();
};
const [InfoModel, infoModelApi] = useVbenModal({
  connectedComponent: Info,
  destroyOnClose: true,
});
const onStatusChange = async (row: any) => {
  let newStatus: number;
  if (row.status == 1) {
    newStatus = -2;
  } else {
    newStatus = 1;
  }
  const status: any = {
    1: '正常',
    '-2': '黑名单',
  };
  try {
    await confirm(
      `你要将用户【${row.nickname}】${newStatus == -2 ? '加入' : '移出'} 【${status[newStatus]}】 吗？`,
      `切换状态`,
    );
    await changeUserStatus({ id: row.id, status: newStatus });
    message.success('操作成功');
    onRefresh();
    return true;
  } catch {
    return false;
  }
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    headerCellConfig: {
      height: 40,
    },
    cellConfig: {
      height: 70,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await getUserList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

function onRefresh() {
  gridApi.query();
}
/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
const confirm = (content: string, title: string) => {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
};
</script>
<template>
  <Page auto-content-height>
    <Grid>
      <template #isBindFace="{ row }">
        <div
          :style="row.isBindFace == 1 ? 'color:hsl(var(--success))' : ''"
          class="flex items-center justify-center"
        >
          <IconifyIcon icon="mdi:face-recognition" class="size-6"></IconifyIcon>
        </div>
      </template>
      <template #action="{ row }">
        <Button
          type="link"
          size="small"
          v-if="row.status == 2"
          @click="onStatusChange(row)"
          >移出黑名单</Button
        >
        <Button
          type="link"
          danger
          size="small"
          v-if="row.status == 1"
          @click="onStatusChange(row)"
          >加入黑名单</Button
        >
        <Button type="link" size="small" @click="showInfo(row)">详情</Button>
      </template>
    </Grid>
    <InfoModel></InfoModel>
  </Page>
</template>
