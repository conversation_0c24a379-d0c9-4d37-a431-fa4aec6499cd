import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getTicketCheckList(params: Recordable<any>) {
  return requestClient.get('/tkt/ticketCheck/list', { params });
}

async function getAllTicketCheckList(params: Recordable<any>) {
  return requestClient.get('/tkt/ticketCheck/all', { params });
}

async function getTicketCheckInfo(id: string) {
  return requestClient.get('/tkt/ticketCheck/info', { params: { id } });
}

async function createTicketCheck(data: Recordable<any>) {
  return requestClient.post('/tkt/ticketCheck/create', data);
}

async function updateTicketCheck(data: Recordable<any>) {
  return requestClient.post('/tkt/ticketCheck/update', data);
}

async function deleteTicketCheck(id: string) {
  return requestClient.post('/tkt/ticketCheck/delete', { id });
}

async function changeTicketCheckStatus(data: Recordable<any>) {
  return requestClient.post('/tkt/ticketCheck/changeStatus', data);
}

export {
  getTicketCheckList,
  getAllTicketCheckList,
  getTicketCheckInfo,
  createTicketCheck,
  updateTicketCheck,
  deleteTicketCheck,
  changeTicketCheckStatus,
};
