<script lang="ts" setup>
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus, ChevronDown } from '@vben/icons';
import {
  Button,
  message,
  Modal,
  Dropdown,
  Menu,
  MenuItem,
  Typography,
} from 'ant-design-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs, ref } from 'vue';
import { useColumns, useGridFormSchema } from './data';
import { getCardList, changeCardStatus, refundCard } from '#/api/manageModule';
import Log from './modules/log.vue';
import Form from './modules/form.vue';

const { accessAllEnums } = toRefs(useAccessStore());

const onCreate = () => {
  formModelApi.setData({ type: 'create' }).open();
};
const onRecharge = (row: any) => {
  formModelApi.setData({ ...row, type: 'recharge' }).open();
  // 充值
};
const onReplace = (row: any) => {
  formModelApi.setData({ ...row, type: 'replace' }).open();
  //   补卡
};
const onRefund = async (row: any) => {
  try {
    await confirm(`是否确认退卡金额：${row.balance}元？`, `退卡`);
    await refundCard({
      id: row.id,
    });
    message.success('操作成功');
    onRefresh();
  } catch (error: any) {
    message.error(error.message);
  }
};

// 余额变更
const onAmountChange = (row: any) => {
  formModelApi.setData({ ...row, type: 'amountChange' }).open();
}

const showLog = (row: any) => {
  logModelApi.setData({ cardId: row.id }).open();
};

const onStatusChange = async (newStatus: any, row: any) => {
  const status: any = {
    0: '禁用',
    1: '启用',
    2: '挂失',
  };
  try {
    await confirm(
      `你要将卡号【${row.cardNo}】的状态切换为 【${status[newStatus]}】 吗？`,
      `切换状态`,
    );
    await changeCardStatus({
      id: row.id,
      status: newStatus,
    });
    message.success('操作成功');
    onRefresh();
  } catch (error: any) {
    message.error(error.message);
  }
};

const onRefresh = () => {
  gridApi.query();
};
const onActionClick = ({ code, row }: { code: any; row: any }) => {
  console.log(code, row, 'code, row');
  if (code == 0) {
    // 禁用
    onStatusChange(0, row);
  } else if (code == 1) {
    // 启用
    onStatusChange(1, row);
  } else if (code == 2) {
    // 挂失
    onStatusChange(2, row);
  } else if (code == 3) {
    // 退卡
    onRefund(row);
  } else if (code == 4) {
    // 补卡
    onReplace(row);
  } else if (code == 5) {
    // 余额变更
    onAmountChange(row);
  }
};

const [FormModel, formModelApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [LogModel, logModelApi] = useVbenModal({
  connectedComponent: Log,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['dateRange', ['beginDate', 'endDate']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await getCardList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
const confirm = (content: string, title: string) => {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
};
</script>
<template>
  <Page auto-content-height>
    <FormModel @confirm="onRefresh"></FormModel>
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" class="mr-2" @click="onCreate">
          <Plus class="size-5" />
          开卡
        </Button>
        <Button type="primary" @click="onRecharge({ type: 'recharge' })">
          <Plus class="size-5" />
          充值
        </Button>
      </template>
      <template #action="{ row }">
        <Button type="link" size="small" v-if="row.status == 1" @click="onRecharge(row)">充值</Button>
        <Button type="link" size="small" @click="showLog(row)">余额明细</Button>
        <Dropdown>
          <Button type="link" size="small">
            <div class="flex items-center">
              更多
              <ChevronDown class="size-4" />
            </div>
          </Button>
          <template #overlay>
            <Menu @click="onActionClick({ code: $event.key, row })">
              <MenuItem :key="5" v-if="row.status == 1"> 余额变更 </MenuItem>
              <MenuItem :key="0" v-if="row.status == 1">
              <Typography>
                <Typography.Text type="danger"> 禁用 </Typography.Text>
              </Typography>
              </MenuItem>
              <MenuItem :key="1" v-if="row.status == 0 || row.status == 2"> 启用 </MenuItem>
              <MenuItem :key="3" v-if="row.cardType == 2 && row.status != 3">
              <Typography>
                <Typography.Text type="danger"> 退卡 </Typography.Text>
              </Typography>
              </MenuItem>
              <MenuItem :key="2" v-if="row.cardType == 2 && row.status != 2">
              挂失
              </MenuItem>
              <MenuItem :key="4" v-if="row.cardType == 2 && row.status == 2">
              补卡
              </MenuItem>
            </Menu>
          </template>
        </Dropdown>
      </template>
    </Grid>
    <LogModel></LogModel>
  </Page>
</template>
