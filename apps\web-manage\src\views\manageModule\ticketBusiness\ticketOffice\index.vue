<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import { Button, message, Modal } from 'ant-design-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useColumns, useGridFormSchema } from './data';
import { getTicketOfficeList, getTicketOfficeInfo, deleteTicketOffice, changeTicketOfficeStatus } from '#/api/manageModule';
import Form from './modules/form.vue';
import TicketList from './modules/ticketList.vue';

const [FormModel, formModelApi] = useVbenModal({
    connectedComponent: Form,
    destroyOnClose: true,
});
const [TicketListModel, ticketListModelApi] = useVbenModal({
    connectedComponent: TicketList,
    destroyOnClose: true,
});
const onCreate = () => {
    formModelApi.setData({}).open();
}
const onEdit = async (row: any) => {
    const res: any = await getTicketOfficeInfo(row.id);
    formModelApi.setData(res).open();
};
const onDelete = async (row: any) => {
    const hideLoading = message.loading({
        content: '删除中...',
        duration: 0,
        key: 'action_process_msg',
    });
    deleteTicketOffice(row.id)
        .then(() => {
            message.success('删除成功');
            onRefresh();
            hideLoading();
        })
        .catch(() => {
            hideLoading();
        });
};
const onActionClick = async ({ code, row }: { code: string; row: any }) => {
    if (code === 'edit') {
        onEdit(row);
    } else if (code === 'delete') {
        onDelete(row);
    } else if (code === 'ticket') {
        const res: any = await getTicketOfficeInfo(row.id);
        ticketListModelApi.setData(res.ticketList).open();
    }
};
const onStatusChange = async (newStatus: any, row: any) => {
    const status: any = {
        0: '禁用',
        1: '启用',
    };
    try {
        await confirm(
            `你要将售票点【${row.pointName}】的状态切换为 【${status[newStatus.toString()]}】 吗？`,
            `切换状态`,
        );
        await changeTicketOfficeStatus({ id: row.id, status: newStatus });
        return true;
    } catch {
        return false;
    }
};
const onRefresh = () => {
    gridApi.query();
};

const [Grid, gridApi] = useVbenVxeGrid({
    formOptions: {
        fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
        schema: useGridFormSchema(),
        collapsed: true,
        submitOnChange: true,
        submitOnEnter: true,
        collapsedRows: 1,
        wrapperClass: 'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
        showCollapseButton: true,
    },

    gridOptions: {
        columns: useColumns(onActionClick, onStatusChange),
        height: 'auto',
        keepSource: true,
        proxyConfig: {
            ajax: {
                query: async ({ page }, formValues) => {
                    const res: any = await getTicketOfficeList({
                        page: page.currentPage,
                        pageSize: page.pageSize,
                        ...formValues,
                    });
                    return {
                        items: res.list,
                        total: res.total,
                    };
                },
            },
        },
        rowConfig: {
            keyField: 'id',
        },

        toolbarConfig: {
            custom: true,
            export: false,
            refresh: { code: 'query' },
            search: true,
            zoom: true,
        },
    } as VxeTableGridOptions<any>,
});

const confirm = (content: string, title: string) => {
    return new Promise((reslove, reject) => {
        Modal.confirm({
            content,
            onCancel() {
                reject(new Error('已取消'));
            },
            onOk() {
                reslove(true);
            },
            title,
        });
    });
};

</script>
<template>
    <Page auto-content-height>
        <FormModel @success="onRefresh"></FormModel>
        <TicketListModel />
        <Grid>
            <template #toolbar-actions>
                <Button type="primary" @click="onCreate">
                    <Plus class="size-5" />
                    新增售票点
                </Button>
            </template>
        </Grid>
    </Page>
</template>
