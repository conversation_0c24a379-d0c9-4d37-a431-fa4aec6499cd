import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getRentalOrderList(params: Recordable<any>) {
  return requestClient.get('/biz/rentalOrder/list', { params });
}
async function getRentalOrderLog(params: Recordable<any>) {
  return requestClient.get('/biz/rentalOrder/log', { params });
}

async function getRentalOrderInfo(id: string) {
  return requestClient.get('/biz/rentalOrder/info', { params: { id } });
}
async function getRentalOrderDetail(params: Recordable<any>) {
  return requestClient.get('/biz/rentalOrder/detail', { params });
}

async function takeGoods(data: Recordable<any>) {
  return requestClient.post('/biz/rentalOrder/take', data);
}


export {
  getRentalOrderList,
  getRentalOrderLog,
  getRentalOrderInfo,
  getRentalOrderDetail,
  takeGoods
};
