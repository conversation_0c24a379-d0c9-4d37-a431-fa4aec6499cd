<script setup lang="ts">
import type { ItemType, MenuProps } from 'ant-design-vue';

import { h, reactive, ref, toRefs } from 'vue';

import { Page, VbenAvatar } from '@vben/common-ui';

import {
  FileProtectOutlined,
  InfoCircleOutlined,
  LockOutlined,
  ProfileOutlined,
} from '@ant-design/icons-vue';
import { Menu } from 'ant-design-vue';
import { useUserStore } from '@vben/stores';
const { userInfo } = toRefs(useUserStore());

import InfoForm from './modules/editInfo.vue';
import PasswordForm from './modules/editPassword.vue';
import LoginLog from './modules/loginLog.vue';
import ActionLog from './modules/actionLog.vue';

const selectedKeys = ref<any>(['info']);

const items: ItemType[] = reactive([
  { key: 'info', icon: () => h(InfoCircleOutlined), label: '修改信息' },
  { key: 'password', icon: () => h(LockOutlined), label: '修改密码' },
  { key: 'loginLog', icon: () => h(FileProtectOutlined), label: '登录日志' },
  { key: 'actionLog', icon: () => h(ProfileOutlined), label: '操作日志' },
]);
const title = ref('修改信息');
const handleClick: MenuProps['onClick'] = (e) => {
  console.log('click', e);
  switch (e.key) {
    case 'info':
      title.value = '修改信息';
      break;
    case 'password':
      title.value = '修改密码';
      break;
    case 'loginLog':
      title.value = '登录日志';
      break;
    case 'actionLog':
      title.value = '操作日志';
      break;
  }
  selectedKeys.value = [e.key];
};
</script>
<template>
  <Page auto-content-height>
    <div class="flex h-full w-full">
      <div class="mr-3 w-[250px] overflow-hidden rounded-md bg-card p-2">
        <div class="mt-3 flex w-full justify-center">
          <VbenAvatar
            :src="userInfo?.avatar"
            :alt="userInfo?.name"
            class="size-24"
          />
        </div>
        <div class="mt-2 w-full">
          <div class="text-center">
            <div class="mb-1 text-lg font-bold">
              {{ userInfo?.name }}
            </div>
            <div class="text-xs">{{ userInfo?.phone }}</div>
          </div>
        </div>
        <div class="mt-5 w-full border-t pt-2">
          <Menu
            v-model:selected-keys="selectedKeys"
            mode="inline"
            :items="items"
            @click="handleClick"
          />
        </div>
      </div>
      <div class="h-full flex-1 overflow-hidden rounded-md bg-card">
        <div class="w-full border-b p-3 pl-4 text-[18px] font-bold">
          {{ title }}
        </div>
        <InfoForm v-if="selectedKeys.includes('info')"></InfoForm>
        <PasswordForm v-if="selectedKeys.includes('password')"></PasswordForm>
        <LoginLog v-if="selectedKeys.includes('loginLog')"></LoginLog>
        <ActionLog v-if="selectedKeys.includes('actionLog')"></ActionLog>
      </div>
    </div>
  </Page>
</template>
<style lang="scss" scoped>
:deep(.ant-menu-root.ant-menu-inline) {
  border: none;
  border-inline-end: none;
}
</style>
