<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import { computed, ref, h, toRefs } from 'vue';
import { getPopupContainer } from '@vben/utils';
import { useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { customOssUpload } from '#/utils/aliyun-oss';
import { useVbenForm } from '#/adapter/form';
import { Button, Radio, RadioGroup, Upload } from 'ant-design-vue';
import { createScenic, updateScenic } from '#/api/manageModule';
import { mapData } from '#/utils/mapData';
import Map from './map.vue';
const emits = defineEmits(['success']);

const formData = ref<any>();

const useFormSchema: any = [
  {
    component: 'Input',
    fieldName: 'scenicName',
    label: '景区名称',
    rules: 'required',
    componentProps: {
      placeholder: '请输入景区名称',
      allowClear: true,
    },
  },
  {
    component: 'TimeRangePicker',
    fieldName: 'businessHours',
    label: '营业时间',
    componentProps: {
      format: 'HH:mm',
      allowClear: true,
      placeholder: ['开始时间', '结束时间'],
      separator: '至',
      valueFormat: 'HH:mm',
    },
  },
  {
    component: 'Input',
    fieldName: 'contact',
    label: '联系方式',
    componentProps: {
      placeholder: '请输入联系方式',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'address',
    label: '详细地址',
    componentProps: {
      placeholder: '请输入详细地址',
      allowClear: true,
    },
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: [
        { label: '启用', value: 1 },
        { label: '不启用', value: 0 },
      ],
      optionType: 'default',
    },
    dependencies: {
      if(values) {
        return !!values.address;
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['address'],
    },
    defaultValue: 1,
    fieldName: 'positioning',
    label: '是否启用定位',
  },
  {
    component: 'Input',
    fieldName: 'latitude',
    label: '定位维度',
    componentProps: {
      placeholder: '请输入定位维度',
      allowClear: true,
    },
    dependencies: {
      if(values) {
        return !!values.position;
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['position'],
    },
  },
  {
    component: 'Input',
    fieldName: 'longitude',
    label: '定位经度',
    componentProps: {
      placeholder: '请输入定位经度',
      allowClear: true,
    },
    dependencies: {
      if(values) {
        return !!values.position;
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['position'],
    },
  },
  {
    component: 'Upload',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-6',
    componentProps: {
      // 允许上传的文件类型
      accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
      // 使用自定义的阿里云上传处理函数
      customRequest: (options: any) =>
        customOssUpload({
          ...options,
          // 设置分片大小
          chunkSize: 1 * 1024 * 1024,
          fileTypeTag: 'scenic',
          onSuccess(res: any, file: any) {
            console.log(res, 'upload');
            // 获取上传成功后的文件地址
            const url = res.url;
            // 将文件地址设置到表单中 - 创建新对象而不是直接修改
            formData.value = { ...formData.value, scenicImage: url };
            // 调用原始的onSuccess回调，确保Upload组件状态更新
            options.onSuccess && options.onSuccess(res, file);
          },
        }),
      disabled: false,
      maxCount: 9,
      multiple: false,
      showUploadList: true,
      listType: 'picture-card',
    },
    fieldName: 'scenicImage',
    label: '景区图片',
    renderComponentContent: () => {
      return {
        default: () =>
          h(IconifyIcon, {
            icon: 'mdi:upload',
            class: 'size-4',
          }),
      };
    },
  },
  {
    component: 'Upload',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-6',
    componentProps: {
      // 允许上传的文件类型
      accept: '.mp4',
      // 使用自定义的阿里云上传处理函数
      customRequest: (options: any) =>
        customOssUpload({
          ...options,
          // 设置分片大小
          chunkSize: 1 * 1024 * 1024,
          fileTypeTag: 'scenic',
          onSuccess(res: any, file: any) {
            console.log(res, 'upload');
            // 获取上传成功后的文件地址
            const url = res.url;
            // 将文件地址设置到表单中 - 创建新对象而不是直接修改
            formData.value = { ...formData.value, scenicVideo: url };
            // 调用原始的onSuccess回调，确保Upload组件状态更新
            options.onSuccess && options.onSuccess(res, file);
          },
        }),
      disabled: false,
      maxCount: 1,
      multiple: false,
      showUploadList: true,
      listType: 'text',
    },
    fieldName: 'scenicVideo',
    label: '景区视频',
    renderComponentContent: () => {
      return {
        default: () =>
          h(
            Button,
            {},
            {
              default: () => {
                const content = [];
                content.push(
                  h(IconifyIcon, {
                    icon: 'mdi:upload',
                    class: 'size-4',
                  }),
                );
                content.push('上传');
                return content;
              },
            },
          ),
      };
    },
  },
  {
    component: 'TextEditor',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-6',
    fieldName: 'introduce',
    label: '景区介绍',
    componentProps: {
      placeholder: '请输入景区介绍',
      allowClear: true,
    },
  },
  {
    component: 'Textarea',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-6',
    fieldName: 'visitorNotice',
    label: '游客须知',
    componentProps: {
      placeholder: '请输入游客须知',
      allowClear: true,
    },
  },
  {
    component: 'Textarea',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-6',
    fieldName: 'scenicNotice',
    label: '景区公告',
    componentProps: {
      placeholder: '请输入景区公告',
      allowClear: true,
    },
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: [
        { label: '启用', value: 1 },
        { label: '不启用', value: 0 },
      ],
      optionType: 'default',
    },
    defaultValue: 1,
    fieldName: 'status',
    label: '状态',
  },
  {
    component: 'Input',
    fieldName: 'listorder',
    label: '排序',
    componentProps: {
      placeholder: '请输入排序',
      allowClear: true,
    },
  },
];

// 表单配置
const [Form, formApi] = useVbenForm({
  schema: useFormSchema,
  showDefaultActions: false,
  layout: 'horizontal',
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
});

// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modelApi.getData<any>();
      formApi.resetForm();
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };

        if (data.scenicImage) {
          newFormData.scenicImage = data.scenicImage
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.scenicImage = [];
        }
        if (data.scenicVideo) {
          newFormData.scenicVideo = data.scenicVideo
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.scenicVideo = [];
        }
        // 赋值给formData.value
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      } else {
        id.value = undefined;
      }
    }
  },
});

// 地图弹窗
const showMap = async () => {
  const values = await formApi.getValues();
  mapModalApi.setData({ ...values }).open();
};
const [MapModal, mapModalApi] = useVbenModal({
  connectedComponent: Map,
  destroyOnClose: false,
});

// 地图弹窗确认
const mapConfirm = async (mapData: any) => {
  console.log('获取定位', mapData);
  const values = await formApi.getValues();
  console.log(formApi.setValues(), 'values');
  formApi.setValues({
    ...values,
    address: mapData.address,
    latitude: mapData.latitude,
    longitude: mapData.longitude,
  });
};

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();

  // 处理图片和附件字段
  ['scenicImage', 'scenicVideo'].forEach((field) => {
    if (values[field]) {
      values[field] = values[field]
        .map((item: any) => item.response.url)
        .join(',');
    }
  });

  return values;
};

// 统一的提交处理函数
const handleSubmit = async (resetAfterSuccess = false) => {
  const values = await processFormValues();
  if (!values) return;
  values.businessHours = values.businessHours.join(' ~ ');
  modelApi.lock();
  try {
    if (id.value) {
      await updateScenic({ id: id.value, ...values });
    } else {
      await createScenic({ ...values });
    }

    emits('success');

    if (resetAfterSuccess) {
      formApi.resetForm();
      modelApi.unlock();
    } else {
      modelApi.close();
    }
  } catch (error) {
    modelApi.unlock();
  }
};

const submitOnEnter = () => handleSubmit();

const getModelTitle = computed(() => {
  return formData.value?.id ? '编辑景区' : '新增景区';
});
</script>
<template>
  <Model class="w-[70%]" :title="getModelTitle">
    <Form>
      <template #positioning="slotProps">
        <RadioGroup v-bind="slotProps">
          <Radio :value="1">启用</Radio>
          <Radio :value="0">不启用</Radio>
        </RadioGroup>
        <Button size="small" @click="showMap" v-if="slotProps.value === 1">
          <template #icon>
            <IconifyIcon icon="mdi:map-marker" class="size-4" />
          </template>
          获取定位
        </Button>
      </template>
      <template #scenicImage="slotProps">
        <div>
          <Upload v-bind="slotProps" list-type="picture-card">
            <IconifyIcon icon="mdi:upload" class="size-4" />
          </Upload>
          <p class="flex items-center text-[12px] text-gray-500">
            <IconifyIcon icon="mdi:info" class="mr-1 size-4" />
            建议图片尺寸不小于375X211px，图片比例16:9，不超过9张
          </p>
        </div>
      </template>
      <template #scenicVideo="slotProps">
        <div>
          <Upload v-bind="slotProps" list-type="text" :maxFileSize="100">
            <Button>
              <IconifyIcon icon="mdi:upload" class="size-4" />
              上传
            </Button>
          </Upload>
          <p class="mt-2 flex items-center text-[12px] text-gray-500">
            <IconifyIcon icon="mdi:info" class="mr-1 size-4" />
            建议视频比例为16:9
          </p>
        </div>
      </template>
    </Form>
    <template #footer v-if="!formData?.id">
      <Button type="default" @click="modelApi.close()">取消</Button>
      <Button type="primary" @click="submitOnEnter">保存</Button>
    </template>
  </Model>
  <MapModal @confirm="mapConfirm" />
</template>
<style lang="scss" scoped></style>
