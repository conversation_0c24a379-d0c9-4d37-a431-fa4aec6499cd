<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import type { PropType } from 'vue';
import { Tree } from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';
import type { DataNode, TreeProps } from 'ant-design-vue/es/tree';

interface CusTreeProps {
    value?: string | string[];
    treeData?: DataNode[];
    multiple?: boolean;
    selectable?: boolean;
    checkable?: boolean;
    checkStrictly?: boolean;
    defaultExpandAll?: boolean;
    fieldNames?: Record<string, string>;
    showIcon?: boolean;
    disabled?: boolean;
    placeholder?: string;
    allowClear?: boolean;
    showSearch?: boolean;
    filterTreeNode?: (treeNode: any) => boolean;
}

const props = withDefaults(defineProps<CusTreeProps>(), {
    value: () => [],
    treeData: () => [],
    multiple: false,
    selectable: false,
    checkable: false,
    checkStrictly: false,
    defaultExpandAll: true,
    fieldNames: () => ({}),
    showIcon: false,
    disabled: false,
    allowClear: true,
    showSearch: false,
});

const emit = defineEmits<{
    'update:value': [value: string | string[]];
    'select': [selectedKeys: string[], info: any];
    'check': [checkedKeys: string[], info: any];
}>();

const modelValue = defineModel<string | string[]>();
const checkedKeys = ref<string[]>([]);
const selectedKeys = ref<string[]>([]);
const expandedKeys = ref<string[]>([]);

// 计算属性：统一处理值
const currentValue = computed(() => {
    return modelValue.value ?? props.value;
});

// 递归获取所有有子节点的节点key
const getAllParentKeys = (nodes: DataNode[], fieldNames: Record<string, string> = {}): string[] => {
    const keyField = fieldNames.key || 'key';
    const childrenField = fieldNames.children || 'children';
    const keys: string[] = [];

    const traverse = (nodeList: DataNode[]) => {
        nodeList.forEach(node => {
            const children = node[childrenField] || node.children;
            if (children && children.length > 0) {
                keys.push(node[keyField] || node.key as string);
                traverse(children);
            }
        });
    };

    traverse(nodes);
    return keys;
};

// 初始化选中状态
const initializeKeys = (value: string | string[]) => {
    if (props.checkable) {
        checkedKeys.value = Array.isArray(value) ? value : value ? [value] : [];
    }
    if (props.selectable) {
        selectedKeys.value = Array.isArray(value) ? value : value ? [value] : [];
    }
};

// 初始化展开状态
const initializeExpandedKeys = () => {
    if (props.defaultExpandAll && props.treeData.length > 0) {
        expandedKeys.value = getAllParentKeys(props.treeData, props.fieldNames);
    }
};

// 监听外部值变化
watch(() => currentValue.value, (newVal) => {
    if (newVal !== undefined) {
        initializeKeys(newVal);
    }
}, { immediate: true, deep: true });

// 监听treeData变化，重新初始化展开状态
watch(() => props.treeData, () => {
    initializeExpandedKeys();
}, { immediate: true, deep: true });

// 监听defaultExpandAll变化
watch(() => props.defaultExpandAll, (newVal) => {
    if (newVal) {
        initializeExpandedKeys();
    } else {
        expandedKeys.value = [];
    }
});

// 处理选中变化
const handleCheck = (checkedKeysValue: string[], info: any) => {
    checkedKeys.value = checkedKeysValue;
    const newValue = props.multiple ? checkedKeysValue : checkedKeysValue[0] || '';

    modelValue.value = newValue;
    emit('update:value', newValue);
    emit('check', checkedKeysValue, info);
};

// 处理选择变化
const handleSelect = (selectedKeysValue: string[], info: any) => {
    selectedKeys.value = selectedKeysValue;
    const newValue = props.multiple ? selectedKeysValue : selectedKeysValue[0] || '';

    modelValue.value = newValue;
    emit('update:value', newValue);
    emit('select', selectedKeysValue, info);
};

// 暴露方法
defineExpose({
    getCheckedKeys: () => checkedKeys.value,
    getSelectedKeys: () => selectedKeys.value,
    getExpandedKeys: () => expandedKeys.value,
    setCheckedKeys: (keys: string[]) => { checkedKeys.value = keys; },
    setSelectedKeys: (keys: string[]) => { selectedKeys.value = keys; },
});
</script>
<template>
    <Tree v-model:checkedKeys="checkedKeys" v-model:selectedKeys="selectedKeys" v-model:expandedKeys="expandedKeys"
        :tree-data="treeData" :selectable="selectable" :checkable="checkable" :checkStrictly="checkStrictly"
        :multiple="multiple" :disabled="disabled" :defaultExpandAll="defaultExpandAll" :field-names="fieldNames"
        :show-icon="showIcon" :allow-drop="() => false" :draggable="false" :show-search="showSearch"
        :filter-tree-node="filterTreeNode" @check="handleCheck" @select="handleSelect" v-if="treeData.length">
        <template #icon="{ meta }" v-if="showIcon">
            <div class="flex items-center justify-center h-full">
                <IconifyIcon v-if="meta?.icon" :icon="meta.icon" class="size-4" />
            </div>
        </template>

        <template #title="{ meta, title }">
            <slot name="title" :meta="meta" :title="title">
                {{ meta?.title ? $t(meta.title) : title }}
            </slot>
        </template>

        <template #switcherIcon="slotProps" v-if="$slots.switcherIcon">
            <slot name="switcherIcon" v-bind="slotProps" />
        </template>
    </Tree>
</template>
