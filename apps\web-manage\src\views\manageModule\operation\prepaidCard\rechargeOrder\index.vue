<script lang="ts" setup>
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs, ref } from 'vue';
import { useColumns, useGridFormSchema } from './data';
import { rechargeOrder } from '#/api/manageModule';
const { accessAllEnums } = toRefs(useAccessStore());

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['dateRange', ['beginDate', 'endDate']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await rechargeOrder({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

const filterCardType = (cardType: any) => {
  return accessAllEnums.value.prepaidCardType.list.find(
    (item: any) => item.value === cardType,
  )?.label;
};
</script>
<template>
  <Page auto-content-height>
    <Grid>
      <template #cardNo="{ row }">
        {{ row.cardInfo.cardNo }}
      </template>
      <template #cardType="{ row }">
        {{ filterCardType(row.cardInfo.cardType) }}
      </template>
      <template #name="{ row }">
        {{ row.cardInfo.name }}
      </template>
      <template #phone="{ row }">
        {{ row.cardInfo.phone }}
      </template>
      <template #amount="{ row }">
        <p>{{ row.amount }}</p>
        <p v-if="Number(row.giftAmount) > 0">（赠：{{ row.giftAmount }}）</p>
      </template>
    </Grid>
  </Page>
</template>
