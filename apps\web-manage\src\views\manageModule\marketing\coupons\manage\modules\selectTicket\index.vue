<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { ref, watch, toRefs } from 'vue';
import { Button, Table } from 'ant-design-vue';
import { useVbenModal } from '@vben/common-ui';
import TicketList from './ticketList.vue';

import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());

const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(['update:value']);
const ticketList = ref<any[]>([]);
const scenicIds = ref<any>(null);
watch(
  () => props.value,
  (newVal) => {
    ticketList.value = newVal;
  },
  {
    immediate: true,
    deep: true,
  },
);

const columns = ref<any>([
  {
    title: '票名',
    dataIndex: 'ticketName',
    width: 200,
  },
  {
    title: '销售价',
    dataIndex: 'sellingPrice',
    width: 100,
  },
  {
    title: '类型',
    dataIndex: 'model',
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 120,
  },
]);

// 门票列表弹窗
const [ModelTicket, modelTicketApi] = useVbenModal({
  connectedComponent: TicketList,
  destroyOnClose: true,
});

const listChange = (data: any) => {
  // 如果data中存在ticketList中的数据，则替换
  let list = [...data];
  ticketList.value = list;
  emit('update:value', ticketList.value);
};

const addTicket = (val: any) => {
  scenicIds.value = val.join(',');
  modelTicketApi.setData({}).open();
};
const delTicket = (val: any) => {
  let data = {
    ...val,
  };
  ticketList.value = changeData(ticketList.value).filter(
    (item: any) => item.id !== data.id,
  );
  emit('update:value', ticketList.value);
};
const changeData = (val: any) => {
  let list = [...val];
  return list;
};

const filterModel = (val: any) => {
  return accessAllEnums.value.ticketModel.list.find(
    (item: any) => item.value === val,
  )?.label;
};

defineExpose({
  addTicket,
});
</script>
<template>
  <div class="w-full">
    <Table
      :columns="columns"
      :dataSource="ticketList"
      :pagination="false"
      bordered
      rowClassName="custom-row"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'model'">
          {{ filterModel(record.model) }}
        </template>
        <template v-if="column.dataIndex === 'action'">
          <Button type="link" danger @click="delTicket(record)">删除</Button>
        </template>
      </template>
    </Table>
    <ModelTicket
      :selectedTickets="changeData(ticketList)"
      :scenicIds="scenicIds"
      @change="listChange"
    >
    </ModelTicket>
  </div>
</template>
<style>
.custom-row .ant-table-cell {
  padding: 6px 16px !important;
}
</style>
