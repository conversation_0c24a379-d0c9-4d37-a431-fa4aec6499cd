import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs, markRaw } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
import { getAllScenicList } from '#/api/manageModule';

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'complaintType',
      label: '投诉类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择投诉类型',
        allowClear: true,
        options: accessAllEnums.value.complaintType.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'complaintStatus',
      label: '办结状态',
      hideLabel: true,
      componentProps: {
        options: [
          { label: '未办结', value: 1 },
          { label: '已办结', value: 2 },
        ],
        placeholder: '请选择办结状态',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'linkperson',
      label: '联系人',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入联系人',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'linktel',
      label: '联系电话',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入联系电话',
        allowClear: true,
      },
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'complaintType',
      title: '投诉类型',
      width: 120,
      formatter: ({ row }: any) => {
        return accessAllEnums.value.complaintType.list.find(
          (item: any) => item.value === row.complaintType,
        )?.label;
      },
    },
    {
      field: 'scenicName',
      title: '所属景区',
      width: 120,
      formatter: ({ row }: any) => {
        return row.scenicInfo.scenicName;
      },
    },
    {
      field: 'complaintImg',
      title: '投诉图片',
      width: 150,
      slots: {
        default: 'complaintImgs',
      },
    },
    {
      field: 'complaintContent',
      title: '投诉内容',
      minWidth: 150,
    },
    {
      field: 'linkperson',
      title: '联系人',
      width: 150,
      slots: {
        default: 'linkperson',
      },
    },
    {
      field: 'createdAt',
      title: '投诉时间',
      width: 150,
    },
    {
      field: 'complaintStatus',
      title: '办结状态',
      width: 150,
      formatter: ({ row }: any) => {
        return row.complaintStatus === 1 ? '未办结' : '已办结';
      },
      cellRender: {
        name: 'CellTag',
        options: [
          { label: '未办结', value: 1, color: 'error' },
          { label: '已办结', value: 2, color: 'success' },
        ],
      },
    },
    {
      field: 'handlerInfo',
      title: '办结人',
      width: 150,
      slots: {
        default: 'handlerInfo',
      },
    },
    {
      field: 'handlTime',
      title: '处理时间',
      minWidth: 150,
    },
    {
      field: 'operation',
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: '操作',
      width: 180,
    },
  ];
}
