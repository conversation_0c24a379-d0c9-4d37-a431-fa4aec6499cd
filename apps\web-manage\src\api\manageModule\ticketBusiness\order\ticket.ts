import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getOrderList(params: Recordable<any>) {
  return requestClient.get('/tkt/order/list', { params });
}

async function getOrderInfo(id: string) {
  return requestClient.get('/tkt/order/info', { params: { id } });
}

async function getOrderVerifyLog(params: Recordable<any>) {
  return requestClient.get('/tkt/order/verifyLog', { params });
}

async function getOrderLog(params: Recordable<any>) {
  return requestClient.get('/tkt/order/log', { params });
}

async function getOrderRefundInfo(params: Recordable<any>) {
  return requestClient.get('/tkt/order/refundInfo', { params });
}

async function getOrderCouponInfo(params: Recordable<any>) {
  return requestClient.get('/tkt/order/couponInfo', { params });
}

async function orderFullRefund(data: Recordable<any>) {
  return requestClient.post('/tkt/order/fullRefund', data);
}

async function orderInitiativeRefund(data: Recordable<any>) {
  return requestClient.post('/tkt/order/initiativeRefund', data);
}

async function orderPartRefund(data: Recordable<any>) {
  return requestClient.post('/tkt/order/partRefund', data);
}

export {
  getOrderList,
  getOrderInfo,
  getOrderVerifyLog,
  getOrderLog,
  getOrderRefundInfo,
  getOrderCouponInfo,
  orderFullRefund,
  orderInitiativeRefund,
  orderPartRefund,
};
