import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getAdminUserList(params: Recordable<any>) {
  return requestClient.get('/sys/adminUser/list', { params });
}
async function getAllUserList(params: Recordable<any>) {
  return requestClient.get('/sys/adminUser/all', { params });
}

async function createUser(data: Recordable<any>) {
  return requestClient.post('/sys/adminUser/create', data);
}

async function updateUser(data: Recordable<any>) {
  return requestClient.post('/sys/adminUser/update', data);
}

async function deleteUser(id: string) {
  return requestClient.post('/sys/adminUser/delete', { id });
}

async function updateUserStatus(data: Recordable<any>) {
  return requestClient.post('/sys/adminUser/updateStatus', data);
}

async function userPasswordReset(id: string) {
  return requestClient.post('/sys/adminUser/passwordReset', { id });
}
export {
  getAdminUserList,
  getAllUserList,
  createUser,
  updateUser,
  deleteUser,
  updateUserStatus,
  userPasswordReset,
};
