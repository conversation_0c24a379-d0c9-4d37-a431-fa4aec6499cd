import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
import { getAllScenicList } from '#/api/manageModule';
import { z } from '#/adapter/form';
// 搜索表单
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '景点名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入景点名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: accessAllEnums.value.status.list,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'spotName',
      title: '景点名称',
      width: 150,
      fixed: 'left',
    },
    {
      field: 'spotImage',
      title: '景点图片',
      width: 150,
      slots: {
        default: 'CellImages',
      },
    },
    {
      field: 'scenicName',
      title: '所属景区',
      width: 150,
      formatter: ({ row }: any) => {
        return row.scenicInfo.scenicName;
      },
    },
    {
      field: 'businessHours',
      minWidth: 100,
      title: '开放时间',
    },
    {
      field: 'spotAddress',
      minWidth: 100,
      title: '景点地址',
    },
    {
      field: 'introduce',
      minWidth: 150,
      title: '景点介绍',
      slots: {
        default: 'introduce',
      },
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 180,
    },
    {
      field: 'status',
      maxWidth: 100,
      title: '状态',
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: onStatusChange,
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'spotName',
          nameTitle: '景点',
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 130,
    },
  ];
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'spotName',
      label: '景点名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入景点名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'TimeRangePicker',
      fieldName: 'businessHours',
      label: '开放时间',
      componentProps: {
        format: 'HH:mm',
        allowClear: true,
        placeholder: ['开始时间', '结束时间'],
        separator: '至',
        valueFormat: 'HH:mm',
      },
    },
    {
      component: 'Input',
      fieldName: 'spotAddress',
      label: '景点地址',
      componentProps: {
        placeholder: '请输入景点地址',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '不启用', value: 0 },
        ],
        optionType: 'default',
      },
      dependencies: {
        if(values) {
          return !!values.spotAddress;
        },
        // 只有指定的字段改变时，才会触发
        triggerFields: ['spotAddress'],
      },
      defaultValue: 1,
      fieldName: 'positioning',
      label: '是否启用定位',
    },
    {
      component: 'Input',
      fieldName: 'latitude',
      label: '定位维度',
      componentProps: {
        placeholder: '请输入定位维度',
        allowClear: true,
      },
      dependencies: {
        if(values) {
          return !!values.position;
        },
        // 只有指定的字段改变时，才会触发
        triggerFields: ['position'],
      },
    },
    {
      component: 'Input',
      fieldName: 'longitude',
      label: '定位经度',
      componentProps: {
        placeholder: '请输入定位经度',
        allowClear: true,
      },
      dependencies: {
        if(values) {
          return !!values.position;
        },
        // 只有指定的字段改变时，才会触发
        triggerFields: ['position'],
      },
    },
    {
      component: 'CusUpload',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-6',
      fieldName: 'spotCover',
      label: '封面图',
      componentProps: {
        maxCount: 1,
        accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
        fileTypeTag: 'spot',
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
      },
      rules: z
        .array(z.object({ url: z.string().url() }))
        .min(1, '请上传封面图'),
    },
    {
      component: 'CusUpload',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-6',
      fieldName: 'spotImage',
      label: '详情图',
      componentProps: {
        maxCount: 9,
        accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
        fileTypeTag: 'spot',
        multiple: true,
        showUploadList: true,
        listType: 'picture-card',
      },
      rules: z
        .array(z.object({ url: z.string().url() }))
        .min(1, '请上传详情图'),
    },
    {
      component: 'CusUpload',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-6',
      fieldName: 'spotAudio',
      label: '景点解说音频',
      componentProps: {
        maxCount: 1,
        accept: '.m4a,.mp3,.wav,.aac',
        fileTypeTag: 'spot',
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
      },
      rules: z
        .array(z.object({ url: z.string().url() }))
        .min(1, '请上传景点解说音频'),
    },
    {
      component: 'TextEditor',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-6',
      fieldName: 'introduce',
      label: '景点介绍',
      componentProps: {
        placeholder: '请输入景点介绍',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '不启用', value: 0 },
        ],
        optionType: 'default',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Input',
      fieldName: 'listorder',
      label: '排序',
      componentProps: {
        placeholder: '请输入排序',
        allowClear: true,
      },
    },
  ];
}
