<script setup lang="ts">
import { useVbenForm } from '#/adapter/form';
import { useInfoFormSchema } from '../data';
import { ref } from 'vue';
import { getAdminUserInfo, updateAdminUser } from '#/api/manageModule';
import { message } from 'ant-design-vue';

const formData = ref<any>();

const [InfoForm, infoFormApi] = useVbenForm({
  schema: useInfoFormSchema(),
  // 一行显示1个
  wrapperClass: 'grid-cols-1',

  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-[500px]',
    },
  },
  handleSubmit: onSubmit,
  actionWrapperClass: 'flex justify-center',
});

async function onSubmit(values: any) {
  console.log(values, 'submit');
  values.avatar = values.avatar.map((item: any) => item.url).join(',');
  const res = await updateAdminUser(values);
  message.success('操作成功');
}

const getInfo = async () => {
  const res = await getAdminUserInfo({});
  infoFormApi.setValues({
    avatar: res.avatar
      ? [
          {
            url: res.avatar,
            uid: res.avatar,
            name: res.avatar,
            status: 'done',
            response: {
              url: res.avatar,
            },
          },
        ]
      : [],
    name: res.name,
    phone: res.phone,
    sex: res.sex,
  });
};
getInfo();
</script>
<template>
  <div class="max-w-[600px] p-5">
    <InfoForm ref="form" />
  </div>
</template>
