<script lang="ts" setup>
import { Switch, TimeRangePicker } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';

const emit = defineEmits(['blur', 'change']);

const modelValue = defineModel<[number, [Dayjs, Dayjs]]>({
  default: () => [0, []],
});

function onChange() {
  emit('change', modelValue.value);
}
</script>
<template>
  <div class="flex h-[32px] w-full items-center gap-1">
    <div class="w-2/5">
      <Switch
        v-model:checked="modelValue[0]"
        :checkedValue="1"
        :unCheckedValue="0"
        @change="onChange"
        @blur="emit('blur', modelValue)"
        class="w-[45px]"
      />
    </div>
    <div class="flex flex-1 items-center" v-if="modelValue[0] === 1">
      <div class="mr-2 w-[120px] text-right text-sm font-[500] leading-6">
        可核销时间
      </div>
      <TimeRangePicker
        v-model:value="modelValue[1]"
        format="HH:mm"
        :placeholder="['开始时间', '结束时间']"
        separator="至"
        valueFormat="HH:mm"
        allow-clear
        @change="onChange"
        @blur="emit('blur', modelValue)"
        class="w-full"
      />
    </div>
  </div>
</template>
