<script setup lang="ts">
import { useV<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>iewer } from '@vben/common-ui';
import { ref } from 'vue';
import { Descriptions, DescriptionsItem, Tag, Collapse, CollapsePanel } from 'ant-design-vue';

const actionInfo = ref<any>({});

const [Drawer, drawerApi] = useVbenDrawer({
    async onConfirm() {
        drawerApi.close();
    },
    onOpenChange(isOpen) {
        if (isOpen) {
            const data = drawerApi.getData<any>();
            if (data) {
                actionInfo.value = data;
                drawerApi.setState({
                    showCancelButton: false,
                })
            }
        }
    },
});
</script>

<template>
    <Drawer class="w-[30%]" title="日志详情">
        <Descriptions size="small" bordered :column="1">
            <DescriptionsItem label="请求ID">{{ actionInfo.traceId }}</DescriptionsItem>
            <DescriptionsItem label="用户">{{
                actionInfo.userInfo.name
                }}</DescriptionsItem>
            <DescriptionsItem label="请求IP">{{ actionInfo.ip }}</DescriptionsItem>
            <DescriptionsItem label="请求时间">{{
                actionInfo.requestTime
                }}</DescriptionsItem>
            <DescriptionsItem label="请求接口">{{
                actionInfo.uri
                }}</DescriptionsItem>
            <DescriptionsItem label="请求方法">{{
                actionInfo.method
                }}</DescriptionsItem>
            <DescriptionsItem label="状态代码" :span="2">
                {{ actionInfo.code }}
            </DescriptionsItem>
            <DescriptionsItem label="状态">
                <Tag :color="actionInfo.status === 1 ? 'success' : 'error'">{{ actionInfo.status === 1 ? '成功' : '失败' }}
                </Tag>
            </DescriptionsItem>
        </Descriptions>
        <Collapse accordion class="mt-3">
            <CollapsePanel key="1" header="请求参数">
                <!-- <div class="code">
                    {{ actionInfo.request }}
                </div> -->
                <JsonViewer :value="JSON.parse(actionInfo.request)" :expand-depth="1" boxed copyable preview-mode :show-array-index="false"></JsonViewer>
            </CollapsePanel>
            <CollapsePanel key="2" header="响应内容">
                <div class="code">
                    {{ actionInfo.response }}
                </div>
            </CollapsePanel>
            <CollapsePanel key="3" header="userAgent">
                <div class="code">
                    <p>浏览器：{{ actionInfo.userAgent.ua }}</p>
                    <p>系统： {{ actionInfo.userAgent.os }}</p>
                    <p>设备类型：{{ actionInfo.userAgent.device }}</p>
                    {{ actionInfo.userAgent.originalUserAgent }}
                </div>
            </CollapsePanel>
        </Collapse>
    </Drawer>
</template>

<style lang="scss" scoped>
.code {
    background: #848484;
    padding: 15px;
    color: #fff;
    font-size: 12px;
    border-radius: 4px;
    white-space: word-break;
    word-break: break-all;
}
</style>