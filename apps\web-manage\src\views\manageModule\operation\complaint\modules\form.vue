<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import {
  Descriptions,
  DescriptionsItem,
  Image,
  message,
  Select,
  Textarea,
} from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
import { complaintDeal } from '#/api/manageModule';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

const emits = defineEmits(['success']);
const complaintInfo = ref<any>({});
const type = ref('edit');
const submitParams = ref<any>({});
const [Model, modelApi] = useVbenModal({
  title: '投诉建议',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    let data = modelApi.getData();
    if (isOpen) {
      complaintInfo.value = data;
      type.value = data.type;
      if (data.feedback || data.complaintStatus) {
        submitParams.value.feedback = data.feedback;
        submitParams.value.complaintStatus = data.complaintStatus;
      }
      if (data.type == 'info') {
        modelApi.setState({
          showCancelButton: false,
          confirmText: '关闭',
        });
      }
    }
  },
});

const handleSubmit = async () => {
  if (type.value == 'info') {
    modelApi.close();
  } else {
    await complaintDeal({ ...submitParams.value, id: complaintInfo.value.id });
    message.success('处理成功');
    modelApi.close();
    emits('success');
  }
};

const filterType = (val: any) => {
  return accessAllEnums.value?.complaintType.list.find(
    (item: any) => item.value === val,
  )?.label;
};

const customLabelStyle = ref({
  width: '200px',
});
const fallback = ref(
  'data:image/png;base64,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',
);
</script>
<template>
  <Model class="w-[50%]">
    <Descriptions title="" bordered :column="2" :label-style="customLabelStyle">
      <DescriptionsItem label="投诉类型">{{
        filterType(complaintInfo.complaintType)
      }}</DescriptionsItem>
      <DescriptionsItem label="所属景区">{{
        complaintInfo.scenicInfo.scenicName
      }}</DescriptionsItem>
      <DescriptionsItem label="投诉时间" :span="2">{{
        complaintInfo.createdAt
      }}</DescriptionsItem>
      <DescriptionsItem label="投诉内容" :span="2">{{
        complaintInfo.complaintContent
      }}</DescriptionsItem>
      <DescriptionsItem label="投诉图片" :span="2">
        <div class="flex gap-2">
          <template v-for="item in complaintInfo.complaintImg.split(',')">
            <Image
              v-if="complaintInfo.complaintImg"
              :src="item"
              style="width: 60px; height: 60px; object-fit: cover"
              :fallback="fallback"
            />
          </template>
        </div>
      </DescriptionsItem>
      <DescriptionsItem label="联系人">{{
        complaintInfo.linkperson
      }}</DescriptionsItem>
      <DescriptionsItem label="联系电话">{{
        complaintInfo.linktel
      }}</DescriptionsItem>
      <DescriptionsItem label="办结状态" :span="2">
        <div v-if="type == 'info'">
          {{ complaintInfo.complaintStatus == 1 ? '未办结' : '已办结' }}
        </div>
        <Select
          v-model:value="submitParams.complaintStatus"
          :options="[
            { label: '未办结', value: 1 },
            { label: '已办结', value: 2 },
          ]"
          placeholder="请选择办结状态"
          class="w-[200px]"
          v-else
        ></Select>
      </DescriptionsItem>
      <DescriptionsItem label="反馈内容" :span="2">
        <div v-if="type == 'info'">
          {{ complaintInfo.feedback }}
        </div>
        <Textarea
          v-model:value="submitParams.feedback"
          placeholder="请输入反馈内容"
          v-else
        ></Textarea>
      </DescriptionsItem>
      <DescriptionsItem
        label="办结人"
        :span="2"
        v-if="complaintInfo.complaintStatus == 2"
      >
        {{ complaintInfo.handlerInfo?.name }}（{{
          complaintInfo.handlerInfo?.phone
        }}）
      </DescriptionsItem>
    </Descriptions>
  </Model>
</template>
