<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import horizontal from './horizontal.vue';
import vertical from './vertical.vue';

const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['update:value']);
const type = ref('horizontal');

// 监听 props.value 的变化来同步 type
watch(
  () => props.value?.templateConfig?.type,
  (newType) => {
    if (newType && newType !== type.value) {
      type.value = newType;
    }
  },
  { immediate: true },
);

const configValue = computed({
  get() {
    if (props.value && props.value.templateConfig) {
      return props.value;
    }
    // 返回默认的横向配置
    return {
      templateConfig: {
        type: type.value,
        ticketImg: [],
        page: {
          width: 150,
          height: 50,
          marginTop: 2, // 横向时上边距为0
          marginBottom: 5,
          marginLeft: 5,
          marginRight: 5,
        },
        blankArea: {
          width: 50,
          position: 'left',
        },
        qrCode: {
          size: 60,
          position: 'top',
        },
        info: {
          ticketCode: true,
          ticketNumber: true,
          personTime: true,
          entryDate: true,
          timeSlot: true,
          seat: true,
          purchaseTime: false,
        },
      },
    };
  },
  set(value) {
    emit('update:value', value);
  },
});

// 配置数据转换函数
const convertConfigData = (currentConfig: any, newType: string) => {
  if (!currentConfig.templateConfig) return currentConfig;

  const oldConfig = currentConfig.templateConfig;
  const newConfig = { ...oldConfig, type: newType };
  console.log('newConfig', oldConfig);
  if (newType === 'vertical' && type.value === 'horizontal') {
    // 横向转竖向：宽度和高度互换，信息区宽度变高度
    newConfig.page = {
      width: oldConfig.page?.height || 50,
      height: oldConfig.page?.width || 150,
      marginTop: oldConfig.page?.marginTop || 5,
      marginBottom: oldConfig.page?.marginBottom || 5,
      marginLeft: oldConfig.page?.marginLeft || 0, // 保持原有左边距，默认为0
      marginRight: oldConfig.page?.marginRight || 5,
    };
    newConfig.blankArea = {
      height: oldConfig.blankArea?.width || 50,
      position: oldConfig.blankArea?.position === 'left' ? 'top' : 'bottom',
    };
    newConfig.qrCode = {
      size: oldConfig.qrCode?.size || 60,
      position: oldConfig.qrCode?.position || 'top',
    };
  } else if (newType === 'horizontal' && type.value === 'vertical') {
    // 竖向转横向
    newConfig.page = {
      width: oldConfig.page?.height || 150,
      height: oldConfig.page?.width || 50,
      marginTop: oldConfig.page?.marginTop || 0, // 保持原有上边距，默认为0
      marginBottom: oldConfig.page?.marginBottom || 5,
      marginLeft: oldConfig.page?.marginLeft || 5,
      marginRight: oldConfig.page?.marginRight || 5,
    };
    newConfig.blankArea = {
      width: oldConfig.blankArea?.height || 100,
      position: oldConfig.blankArea?.position === 'top' ? 'left' : 'right',
    };
    newConfig.qrCode = {
      size: oldConfig.qrCode?.size || 60,
      position: oldConfig.qrCode?.position || 'top',
    };
  }

  return {
    ...currentConfig,
    templateConfig: newConfig,
  };
};

// 切换横向竖向
const toggleOrientation = () => {
  console.log('toggleOrientation 被调用，当前类型:', type.value);

  const newType = type.value === 'horizontal' ? 'vertical' : 'horizontal';
  const currentConfig = configValue.value;

  console.log('准备切换到:', newType, '当前配置:', currentConfig);

  // 转换配置数据
  const convertedConfig = convertConfigData(currentConfig, newType);

  console.log('转换后的配置:', convertedConfig);

  // 先更新类型，强制触发视图更新
  type.value = newType;

  // 然后通过 emit 更新配置值
  emit('update:value', convertedConfig);

  console.log('切换完成，新类型:', type.value);
};
</script>
<template>
  <div>
    <horizontal
      v-model:value="configValue"
      v-if="type === 'horizontal'"
      :key="'horizontal-' + type"
      @toggle-orientation="toggleOrientation"
    />
    <vertical
      v-model:value="configValue"
      v-if="type === 'vertical'"
      :key="'vertical-' + type"
      @toggle-orientation="toggleOrientation"
    />
  </div>
</template>
