<script lang="ts" setup>
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs, ref } from 'vue';
import { Button, Table } from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';
import { useColumns, useGridFormSchema } from './data';
import { getOrderRefundList } from '#/api/manageModule';
import { router } from '#/router';
const { accessAllEnums } = toRefs(useAccessStore());
const refundList = ref<any>([]);
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['applyDate', ['applyStartDate', 'applyEndDate']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },
  gridEvents: {
    // pageChange: ({ currentPage }: any) => {
    //   setAllExpand(true);
    // },
  },
  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    border: 'inner',
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await getOrderRefundList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          //循环数据
          res.list.forEach((item: any) => {
            item.isParent = true;
            if (item.refundItemList && item.refundItemList.length) {
              item.refundItemList.forEach((item2: any, index: number) => {
                item2.isParent = false;
                item2.orderId = item.id; // 添加订单ID引用
                item2.itemIndex = index; // 添加项目索引
                item2.refundStatus = item.refundStatus;
                item2.applyTime = item.applyTime;
              });
            }
          });
          setAllExpand(true);
          refundList.value = res.list;
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    virtualXConfig: {
      enabled: false,
    },
    virtualYConfig: {
      enabled: false,
    },
    spanMethod({ row, column }) {
      if (row.refundItemList && row.refundItemList.length) {
        if (column.field === 'ticketName') {
          return { rowspan: 1, colspan: 7 };
        }
      }
      return { rowspan: 1, colspan: 1 };
    },
    expandConfig: {
      mode: 'inside',
      expandAll: true,
      reserve: true,
    },
    treeConfig: {
      rowField: 'id',
      parentField: 'orderId',
      childrenField: 'refundItemList',
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

const setAllExpand = (value: any) => {
  setTimeout(() => {
    gridApi.grid.setAllRowExpand(value);
  }, 50);
};

const columns: TableColumnType[] = [
  {
    title: '门票信息',
    dataIndex: 'ticketName',
    fixed: 'left',
    minWidth: 300,
    customRender: ({ record }: any) => {
      return record.ticketName || '--';
    },
  },
  {
    title: '退票数量',
    dataIndex: 'refundNum',
    width: 120,
    align: 'center',
  },
  {
    title: '退款金额',
    dataIndex: 'refundPrice',
    width: 120,
    align: 'center',
  },
  {
    title: '退款手续费',
    dataIndex: 'refundFeePrice',
    width: 120,
    align: 'center',
  },
  {
    title: '退款状态',
    dataIndex: 'refundStatus',
    width: 120,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder = refundList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'refundStatus',
        record.itemIndex,
        currentOrder?.refundItemList || [],
      );
    },
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderRefundStatus.list.find(
        (item: any) => item.value === record.refundStatus,
      )?.label;
    },
  },
  {
    title: '申请时间',
    dataIndex: 'applyTime',
    width: 180,
    align: 'center',
    customCell: (record: any) => {
      // 使用记录中的 itemIndex 而不是表格的 index
      const currentOrder = refundList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'applyTime',
        record.itemIndex,
        currentOrder?.refundItemList || [],
      );
    },
  },
  {
    title: '',
    dataIndex: 'options',
    width: 120,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder = refundList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'options',
        record.itemIndex,
        currentOrder?.refundItemList || [],
      );
    },
  },
];

// 需要合并单元格的字段
const mergeFields = ['refundStatus', 'applyTime', 'options'];

// 单元格合并函数
const getMergeProps = (
  field: string,
  index: number | undefined,
  dataSource: any[],
) => {
  // 只有需要合并的字段才进行合并处理
  if (!mergeFields.includes(field) || index === undefined) {
    return {};
  }

  // 如果是第一行，则显示合并的单元格
  if (index === 0) {
    return {
      rowSpan: dataSource.length,
    };
  }

  // 其他行不显示
  return {
    rowSpan: 0,
  };
};
const filterModel = (model: any) => {
  return accessAllEnums.value?.ticketModel.list.find(
    (item: any) => item.value === model,
  )?.label;
};
const showInfo = async (row: any) => {
  router.push({
    name: 'refundDetail',
    query: { id: row.id },
  });
};
</script>
<template>
  <Page auto-content-height>
    <Grid>
      <template #expand_header="{ row }">
        <div v-if="row.isParent" class="flex items-center justify-between">
          <div>
            <span>ID：{{ row.id }}</span>
            <span class="ml-5">订单号：{{ row.orderNo }}</span>
            <span class="ml-5">退单编号：{{ row.refundNo }}</span>
          </div>
          <div class="flex w-[105px] justify-center gap-2">
            <Button type="link" size="small" @click="showInfo(row)"
              >详情</Button
            >
          </div>
        </div>
        <div v-else>{{ row.ticketName }}</div>
      </template>
      <template #expand_content="{ row }">
        <Table
          :key="row.id"
          :columns="columns"
          :data-source="row.refundItemList"
          :pagination="false"
          :show-header="false"
          :scroll="{ x: true }"
          :row-key="(record: any) => record.id || record.ticketId"
          :bordered="true"
          class="expand-table"
        ></Table>
      </template>
    </Grid>
  </Page>
</template>

<style scoped>
.expand-table {
  margin: 8px 0px;
}

.expand-table :deep(.ant-table-tbody > tr > td) {
  padding: 8px 16px;
}
.expand-table :deep(.ant-table-container) {
  border-start-start-radius: 0px !important;
  border-start-end-radius: 0px !important;
}
.expand-table :deep(.ant-table-container table) {
  border-radius: 0px !important;
}
.expand-table :deep(.ant-table-tbody > tr > td:last-child[rowspan]) {
  border-right: none !important;
}
</style>
