<script lang="ts" setup>
import type { DataNode } from 'ant-design-vue/es/tree';

import type { Recordable } from '@vben/types';

import { computed, ref } from 'vue';

import { useVbenDrawer, VbenTree } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { message, Spin, Tree, RadioGroup } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { updateRolePermissions, getRolePermissions } from '#/api/manageModule';
import { $t } from '#/locales';

import { usePermissionFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<any>();

const [Form, formApi] = useVbenForm({
  schema: usePermissionFormSchema(),
  showDefaultActions: false,
  commonConfig: {
    // 所有表单项
    labelWidth: 100,
    componentProps: {
      class: 'w-full',
    },
  },
});

const permissions = ref<DataNode[]>([]);
const loadingPermissions = ref(false);

const id = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();
    drawerApi.lock();
    updateRolePermissions({
      id: id.value,
      permissionIds: values.permissionIds,
      module: values.module,
    })
      .then(() => {
        message.success('设置成功');
        emits('success');
        drawerApi.close();
      })
      .catch(() => {
        drawerApi.unlock();
      });
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<any>();
      formApi.resetForm();
      if (data) {
        id.value = data.id;
        loadPermissions(data.id, data.module);
      }
    }
  },
});

async function loadPermissions(id: string, module: string) {
  loadingPermissions.value = true;
  try {
    const res = await getRolePermissions({ id: id, module: module });
    permissions.value = res.menuTree as unknown as DataNode[];
    if (res.permissions && res.permissions.length > 0) {
      formApi.setFieldValue('permissionIds', res.permissions);
    } else {
      formApi.setFieldValue('permissionIds', []);
    }
  } finally {
    loadingPermissions.value = false;
  }
}

function changeModule(val: any) {
  loadPermissions(id.value, val.target.value);
}

function getNodeClass(node: Recordable<any>) {
  const classes: string[] = [];
  if (node.value?.type === 'button') {
    classes.push('inline-flex');
    if (node.index % 3 >= 1) {
      classes.push('!pl-0');
    }
  }
  if (node.level > 1) {
    classes.push('ml-[10px]');
  }

  return classes.join(' ');
}
</script>
<template>
  <Drawer class="w-[40%]" title="权限设置">
    <Form>
      <template #module="slotProps">
        <RadioGroup v-bind="slotProps" @change="changeModule" />
      </template>
      <template #permissionIds="slotProps">
        <Spin :spinning="loadingPermissions" wrapper-class-name="w-full">
          <VbenTree
            :tree-data="permissions"
            multiple
            bordered
            :default-expanded-level="2"
            :get-node-class="getNodeClass"
            v-bind="slotProps"
            value-field="id"
            label-field="meta.title"
            icon-field="meta.icon"
          >
            <template #node="{ value }">
              <IconifyIcon v-if="value.meta.icon" :icon="value.meta.icon" />
              {{ $t(value.meta.title) }}
            </template>
          </VbenTree>
          <!-- <CusTree v-bind="slotProps" :tree-data="permissions" multiple checkable showIcon
                        :defaultExpandAll="true" :fieldNames="{ key: 'id' }">
                        <template #icon="{ meta }">
                            <div class="flex items-center justify-center h-full">
                                <IconifyIcon v-if="meta.icon" :icon="meta.icon" class="size-4" />
                            </div>
                        </template>
                        <template #title="{ meta }">
                            {{ $t(meta.title) }}
                        </template>
                    </CusTree> -->
        </Spin>
      </template>
    </Form>
  </Drawer>
</template>
<style lang="scss" scoped>
:deep(.ant-tree-title) {
  .tree-actions {
    display: none;
    margin-left: 20px;
  }
}

:deep(.ant-tree-title:hover) {
  .tree-actions {
    display: flex;
    flex: auto;
    justify-content: flex-end;
    margin-left: 20px;
  }
}
</style>
