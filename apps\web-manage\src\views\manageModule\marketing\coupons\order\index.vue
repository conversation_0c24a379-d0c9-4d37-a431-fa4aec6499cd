<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { Modal, message } from 'ant-design-vue';
import { useColumns, useGridFormSchema } from './data';
import { getCouponLogList, revokeCouponLog } from '#/api/manageModule';

const onActionClick = async ({ code, row }: { code: any; row: any }) => {
  if (code === 'revoke') {
    try {
      await confirm(
        '确认作废优惠券【' + row.couponInfo.couponName + '】吗？',
        '作废优惠券',
      );
      await revokeCouponLog(row.id);
      message.success('操作成功');
      gridApi.query();
    } catch (error: any) {
      message.error(error.message);
    }
  }
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['dateRange', ['beginDate', 'endDate']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await getCouponLogList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
const confirm = (content: string, title: string) => {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
};
</script>
<template>
  <Page auto-content-height>
    <Grid></Grid>
  </Page>
</template>
