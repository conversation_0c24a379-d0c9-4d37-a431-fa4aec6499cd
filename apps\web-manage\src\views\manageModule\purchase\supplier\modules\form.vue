<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import { computed, ref, h, toRefs } from 'vue';
import { getPopupContainer } from '@vben/utils';
import { useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { customOssUpload } from '#/utils/aliyun-oss';
import { useVbenForm } from '#/adapter/form';
import { Button } from 'ant-design-vue';
import { createSupplier, updateSupplier } from '#/api/purchase/supplier';
import { useAccessStore } from '@vben/stores';
import { mapData } from '#/utils/mapData';
const { accessAllEnums } = toRefs(useAccessStore());
const emits = defineEmits(['success']);

const formData = ref<any>();

const useFormSchema: any = [
  {
    component: 'Divider',
    fieldName: 'divider1',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-0',
    hideLabel: true,
    renderComponentContent() {
      return {
        default: () => '基础信息',
      };
    },
  },
  {
    component: 'Input',
    fieldName: 'distributorName',
    label: '供应商名称',
    rules: 'required',
    componentProps: {
      placeholder: '请输入供应商名称',
      allowClear: true,
    },
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: accessAllEnums.value.distributorType.list,
      optionType: 'default',
    },
    defaultValue: 1,
    fieldName: 'distributorType',
    label: '供应商类型',
  },
  // {
  //   component: 'Input',
  //   fieldName: 'serialNumber',
  //   label: '供应商编码',
  //   help: '为空时自动生成',
  // },
  {
    component: 'Input',
    label: '联系人',
    fieldName: 'contactName',
    componentProps: {
      placeholder: '请输入联系人',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    label: '联系电话',
    fieldName: 'contactPhone',
    componentProps: {
      placeholder: '请输入联系电话',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    label: '对接负责人',
    fieldName: 'principal',
    componentProps: {
      placeholder: '请输入对接负责人',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    label: '来源',
    fieldName: 'source',
    componentProps: {
      placeholder: '请输入来源',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    label: '地区',
    fieldName: 'region',
    componentProps: {
      options: mapData,
      class: 'w-full',
      placeholder: '请选择地区',
      allowClear: true,
    },
    rules: 'required',
  },
  {
    component: 'Input',
    label: '详细地址',
    fieldName: 'address',
    componentProps: {
      placeholder: '请输入详细地址',
      allowClear: true,
    },
  },
  {
    component: 'TagInput',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-6',
    fieldName: 'tags',
    componentProps: {
      class: 'w-full',
      separator: ',',
      placeholder: '请输入标签',
    },
    label: '标签',
  },
  {
    component: 'Upload',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-6',
    componentProps: {
      // 允许上传的文件类型
      accept: '.',
      // 使用自定义的阿里云上传处理函数
      customRequest: (options: any) =>
        customOssUpload({
          ...options,
          // 设置分片大小
          chunkSize: 1 * 1024 * 1024,
          fileTypeTag: 'materials',
          onSuccess(res: any, file: any) {
            console.log(res, 'upload');
            // 获取上传成功后的文件地址
            const url = res.url;
            // 将文件地址设置到表单中 - 创建新对象而不是直接修改
            formData.value = { ...formData.value, attachments: url };
            // 调用原始的onSuccess回调，确保Upload组件状态更新
            options.onSuccess && options.onSuccess(res, file);
          },
        }),
      disabled: false,
      maxCount: 9,
      multiple: false,
      showUploadList: true,
      listType: 'text',
    },
    fieldName: 'attachments',
    label: '附件',
    renderComponentContent: () => {
      return {
        default: () =>
          h(
            Button,
            {},
            {
              default: () => {
                const content = [];
                content.push(
                  h(IconifyIcon, {
                    icon: 'mdi:upload',
                    class: 'size-4',
                  }),
                );
                content.push('上传');
                return content;
              },
            },
          ),
      };
    },
  },
  {
    component: 'Textarea',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-6',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      allowClear: true,
    },
  },
];

// 表单配置
const [Form, formApi] = useVbenForm({
  schema: useFormSchema,
  showDefaultActions: false,
  layout: 'horizontal',
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
});
// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();
    console.log(values, 'values');
    if (values.productImg) {
      values.productImg = values.productImg
        .map((item: any) => {
          return item.response.url;
        })
        .join(',');
    }
    if (values.attachments) {
      values.attachments = values.attachments
        .map((item: any) => {
          return item.response.url;
        })
        .join(',');
    }
    console.log(values, 'values');
    modelApi.lock();
    (id.value
      ? updateSupplier({ id: id.value, ...values })
      : createSupplier({ ...values })
    )
      .then(() => {
        emits('success');
        modelApi.close();
      })
      .catch(() => {
        modelApi.unlock();
      });
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modelApi.getData<any>();
      formApi.resetForm();
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };

        if (data.productImg) {
          newFormData.productImg = data.productImg
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.productImg = [];
        }
        if (data.attachments) {
          newFormData.attachments = data.attachments
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.attachments = [];
        }
        // 赋值给formData.value
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      } else {
        id.value = undefined;
      }
    }
  },
});

const submitOnEnter = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return;
  const values = await formApi.getValues();
  console.log(values, 'values');
  if (values.productImg) {
    values.productImg = values.productImg
      .map((item: any) => {
        return item.response.url;
      })
      .join(',');
  }
  if (values.attachments) {
    values.attachments = values.attachments
      .map((item: any) => {
        return item.response.url;
      })
      .join(',');
  }
  console.log(values, 'values');
  modelApi.lock();
  createSupplier({ ...values })
    .then(() => {
      emits('success');
      modelApi.close();
    })
    .catch(() => {
      modelApi.unlock();
    });
};

const submitAndAdd = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return;
  const values = await formApi.getValues();
  console.log(values, 'values');
  if (values.productImg) {
    values.productImg = values.productImg
      .map((item: any) => {
        return item.response.url;
      })
      .join(',');
  }
  if (values.attachments) {
    values.attachments = values.attachments
      .map((item: any) => {
        return item.response.url;
      })
      .join(',');
  }
  console.log(values, 'values');
  modelApi.lock();
  createSupplier({ ...values })
    .then(() => {
      emits('success');
      formApi.resetForm();
      modelApi.unlock();
    })
    .catch(() => {
      modelApi.unlock();
    });
};
const getModelTitle = computed(() => {
  return formData.value?.id ? '编辑供应商' : '新增供应商';
});
</script>
<template>
  <Model class="w-[70%]" :title="getModelTitle">
    <Form />
    <template #footer v-if="!formData?.id">
      <Button type="default" @click="modelApi.close()">取消</Button>
      <Button type="primary" @click="submitOnEnter">保存</Button>
      <Button type="primary" @click="submitAndAdd">保存并新增</Button>
    </template>
  </Model>
</template>
<style lang="scss" scoped></style>
