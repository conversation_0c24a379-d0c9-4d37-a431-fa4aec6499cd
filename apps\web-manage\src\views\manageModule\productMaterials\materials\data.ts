import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

import { $t } from '#/locales';
// 搜索表单
export function useGridFormSchema(mode: number): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: modeText(mode) + '名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入' + modeText(mode) + '名称',
      },
    },
    {
      component: 'Select',
      fieldName: 'source',
      label: '来源',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择' + modeText(mode) + '来源',
        options: [
          { label: '自产', value: 1 },
          { label: '采购', value: 2 },
          { label: '委外', value: 3 },
        ],
      },
    },
    {
      component: 'Select',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择' + modeText(mode) + '状态',
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
      },
      fieldName: 'status',
      label: modeText(mode) + '状态',
    },
  ];
}

export function useColumns<T = any>(
  mode: number,
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'serialNumber',
      title: modeText(mode) + '编号',
      width: 150,
      fixed: 'left',
    },
    {
      field: 'productName',
      title: modeText(mode) + '名称',
      width: 150,
      fixed: 'left',
    },
    {
      field: 'typeInfo.typeName',
      title: modeText(mode) + '所属类别',
      width: 150,
    },
    {
      field: 'costPrice',
      minWidth: 100,
      title: '成本单价(元)',
    },
    {
      field: 'salePrice',
      minWidth: 100,
      title: '销售单价(元)',
    },
    {
      field: 'status',
      title: '状态',
      cellRender: { name: 'CellTag' },
      width: 100,
    },
    {
      field: 'source',
      title: '来源',
      width: 100,
      formatter: ({ row }: any) => {
        return row.source == 1 ? '自产' : row.source == 2 ? '采购' : '委外';
      },
    },
    {
      field: 'specification',
      title: '规格型号',
      width: 150,
    },
    {
      field: 'color',
      title: modeText(mode) + '颜色',
      width: 100,
    },
    {
      field: 'useUnit',
      title: '单位',
      width: 100,
    },
    {
      field: 'storageUnit',
      title: '仓储单位',
      width: 100,
    },
    {
      field: 'productImg',
      formatter: (row: any) => {
        return row.productImg.split(',')[0];
      },
      slots: {
        default: 'cellImages',
      },
      title: '图片',
      width: 70,
    },

    {
      field: 'remark',
      minWidth: 100,
      title: '备注',
    },

    {
      field: 'createdAt',
      title: '创建时间',
      width: 165,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'productName',
          nameTitle: modeText(mode),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 130,
    },
  ];
}

export function modeText(mode: number) {
  let text = '';
  if (mode === 1) {
    text = '产品';
  } else if (mode === 2) {
    text = '半成品';
  } else if (mode === 3) {
    text = '物料';
  }
  return text;
}
