<script setup lang="ts">
import type { Recordable } from '@vben/types';

import { computed, ref, h, toRefs } from 'vue';
import { getPopupContainer } from '@vben/utils';
import { useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { useVbenForm } from '#/adapter/form';
import { useFormSchema } from '../data';
import { createHoliday, updateHoliday } from '#/api/manageModule';
const emits = defineEmits(['success']);

const formData = ref<Recordable<any>>({});
// 表单配置
const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1',
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
});

// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    const data = modelApi.getData<any>();
    formApi.resetForm();
    if (isOpen) {
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };
        newFormData.dateRange = [newFormData.beginDate, newFormData.endDate];
        // 赋值给formData.value
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      }
    }
  },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();
  values.beginDate = values.dateRange[0];
  values.endDate = values.dateRange[1];

  return values;
};

// 统一的提交处理函数
const handleSubmit = async () => {
  const values = await processFormValues();
  if (!values) return;
  modelApi.lock();
  try {
    if (id.value) {
      await updateHoliday({ id: id.value, ...values });
    } else {
      await createHoliday({ ...values });
    }
    emits('success');
    modelApi.close();
  } catch (error) {
    modelApi.unlock();
  }
};

const getModelTitle = computed(() => {
  return formData.value?.id ? '编辑节假日' : '新增节假日';
});
</script>

<template>
  <Model class="w-[500px]" :title="getModelTitle">
    <Form />
  </Model>
</template>
