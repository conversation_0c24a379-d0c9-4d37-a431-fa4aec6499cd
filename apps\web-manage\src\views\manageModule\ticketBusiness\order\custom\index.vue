<script lang="ts" setup>
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs, ref } from 'vue';
import { Button } from 'ant-design-vue';
import { useColumns, useGridFormSchema } from './data';
import { getCustomizeOrderList } from '#/api/manageModule';
const { accessAllEnums } = toRefs(useAccessStore());
import { router } from '#/router';

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['dateRange', ['beginDate', 'endDate']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await getCustomizeOrderList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});
const goDetail = (row: any) => {
  console.log(row);
  router.push({
    name: 'orderCustomizeDetail',
    query: { id: row.id },
  });
};
</script>
<template>
  <Page auto-content-height>
    <Grid>
      <template #sellerInfo="{ row }">
        {{ row.sellerInfo?.name }}
      </template>
      <template #operation="{ row }">
        <Button type="link" size="small" @click="goDetail(row)">详情</Button>
      </template>
    </Grid>
  </Page>
</template>
