import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'cardNo',
      label: '卡号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入卡号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'cardType',
      label: '卡类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择卡类型',
        allowClear: true,
        options: [
          { label: '虚拟卡', value: 1 },
          { label: '实体卡', value: 2 },
        ],
      },
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '姓名',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入姓名',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '手机号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入手机号',
        allowClear: true,
      },
    },

    {
      component: 'Select',
      fieldName: 'status',
      label: '卡状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择卡状态',
        allowClear: true,
        options: accessAllEnums.value?.prepaidCardStatus.list,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'dateRange',
      label: '开卡时间',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['开卡开始日期', '开卡结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}
export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'cardNo',
      title: '卡号',
      minWidth: 150,
    },
    {
      field: 'cardType',
      title: '卡类型',
      width: 100,
      formatter: (row: any) => {
        return row.cardType === 1 ? '虚拟卡' : '实体卡';
      },
    },
    {
      field: 'name',
      title: '姓名',
      width: 120,
    },
    {
      field: 'phone',
      title: '手机号',
      width: 120,
    },
    {
      field: 'idcard',
      title: '身份证',
      minWidth: 150,
    },
    {
      field: 'balance',
      title: '余额',
      width: 120,
    },
    {
      field: 'status',
      title: '状态',
      width: 120,
      cellRender: {
        name: 'CellTag',
        options: accessAllEnums.value?.prepaidCardStatus.list.map(
          (item: any) => {
            return {
              value: item.value,
              label: item.label,
              color: type[item.value],
            };
          },
        ),
      },
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.prepaidCardStatus.list.find(
          (item: any) => item.value === row.status,
        )?.label;
      },
    },
    {
      field: 'lastTradeTime',
      title: '最后交易时间',
      width: 200,
    },
    {
      field: 'operation',
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: '操作',
      width: 200,
    },
  ];
}
const type: any = {
  0: 'error',
  1: 'success',
  2: 'warning',
  3: 'error',
};
export function useFormSchema(): VbenFormSchema[] {
  return [];
}
