import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getPrintTemplateList(params: Recordable<any>) {
  return requestClient.get('/sys/printTemplate/list', { params });
}

async function getPrintTemplateAllList(params: Recordable<any>) {
  return requestClient.get('/sys/printTemplate/all', { params });
}

async function getPrintTemplateInfo(id: string) {
  return requestClient.get('/sys/printTemplate/info', { params: { id } });
}

async function createPrintTemplate(data: Recordable<any>) {
  return requestClient.post('/sys/printTemplate/create', data);
}

async function updatePrintTemplate(data: Recordable<any>) {
  return requestClient.post('/sys/printTemplate/update', data);
}

async function deletePrintTemplate(id: string) {
  return requestClient.post('/sys/printTemplate/delete', { id });
}

async function changePrintTemplateStatus(data: Recordable<any>) {
  return requestClient.post('/sys/printTemplate/changeStatus', data);
}

async function setPrintTemplateConfig(data: Recordable<any>) {
  return requestClient.post('/sys/printTemplate/setConf', data);
}

export {
  getPrintTemplateList,
  getPrintTemplateAllList,
  getPrintTemplateInfo,
  createPrintTemplate,
  updatePrintTemplate,
  deletePrintTemplate,
  changePrintTemplateStatus,
  setPrintTemplateConfig,
};
