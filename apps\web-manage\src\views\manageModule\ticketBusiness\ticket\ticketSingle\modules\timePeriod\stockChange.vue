<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { computed, ref, h, toRefs, watch } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import {
  Form,
  FormItem,
  InputNumber,
  Button,
  Table,
  Switch,
} from 'ant-design-vue';

const emit = defineEmits(['confirm']);
const [ModelStock, modelStockApi] = useVbenModal({
  title: '库存设置',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    let data = modelStockApi.getData<any>();

    if (isOpen) {
      stockData.value = data.map((item: any) => {
        return {
          ...item,
          time: `${item.beginTime} - ${item.endTime}`,
          isCustomStock: false,
          isCustomPrice: false,
        };
      });
      console.log(stockData.value, 'data');
    }
  },
});

const formData = ref<Recordable<any>>({});
const stockData = ref<Recordable<any>>([]);
const columns = ref<any[]>([
  {
    title: '时段',
    dataIndex: 'time',
    width: 120,
  },
  {
    title: '自定义库存',
    dataIndex: 'isCustomStock',
    width: 120,
    align: 'center',
  },
  {
    title: '库存',
    dataIndex: 'periodStock',
    align: 'center',
    width: 230,
  },
  {
    title: '自定义价格',
    dataIndex: 'isCustomPrice',
    width: 120,
    align: 'center',
  },
  {
    title: '价格',
    dataIndex: 'periodPrice',
    align: 'center',
    width: 230,
  },
]);
const handleSetStock = () => {
  console.log(formData.value.stock, 'stockData');
  stockData.value = stockData.value.map((item: any) => {
    return {
      ...item,
      periodStock: formData.value.stock,
    };
  });
};
const handleSubmit = () => {
  console.log(stockData.value, 'stockData');
  emit('confirm', stockData.value);
  modelStockApi.close();
};
</script>

<template>
  <ModelStock class="w-[45%]">
    <Form
      :model="formData"
      :labelCol="{
        style: {
          fontSize: '14px',
          lineHeight: '22px',
          fontWeight: '500',
          textAlign: 'right',
          marginRight: '8px',
          width: '100px',
        },
      }"
    >
      <FormItem label="库存统一设置" :colon="false">
        <div class="flex items-center">
          <InputNumber
            :min="0"
            :max="1000000"
            placeholder="请输入库存"
            class="flex-1"
            v-model:value="formData.stock"
          />
          <Button type="primary" class="ml-2" @click="handleSetStock"
            >设置</Button
          >
        </div>
      </FormItem>
    </Form>
    <Table
      :columns="columns"
      :dataSource="stockData"
      :pagination="false"
      bordered
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'isCustomStock'">
          <Switch v-model:checked="record.isCustomStock" />
        </template>
        <template v-if="column.dataIndex === 'isCustomPrice'">
          <Switch v-model:checked="record.isCustomPrice" />
        </template>
        <template v-if="['periodStock'].includes(column.dataIndex)">
          <InputNumber
            :min="0"
            :max="1000000"
            placeholder="请输入库存"
            class="w-full"
            v-model:value="record[column.dataIndex]"
            v-if="record.isCustomStock"
          />
          <template v-else>{{ text == -1 ? '' : text }}</template>
        </template>
        <template v-if="['periodPrice'].includes(column.dataIndex)">
          <InputNumber
            :min="0"
            :max="1000000"
            placeholder="请输入价格"
            class="w-full"
            v-model:value="record[column.dataIndex]"
            v-if="record.isCustomPrice"
          />
          <template v-else>{{ text == -1 ? '' : text }}</template>
        </template>
      </template>
    </Table>
  </ModelStock>
</template>
