<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import {
  Checkbox,
  Button,
  Row,
  Col,
  InputNumber,
  Divider,
  QRCode,
  Select,
} from 'ant-design-vue';
import CusUpload from '#/components/CusUpload/index.vue';
import CusQrCode from '#/components/CusQrCode/index.vue';
const { Option } = Select;
import ticketBg from '#/assets/images/ticketBg.jpg';

const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['update:value', 'toggle-orientation']);

// 配置项状态
let config = reactive({
  type: 'horizontal',
  ticketImg: [],
  // 页面设置
  page: {
    width: 150, // 纸张宽度(mm)
    height: 50, // 纸张高度(mm)
    marginTop: 0, // 横向时上边距为0
    marginBottom: 5,
    marginLeft: 5,
    marginRight: 5,
  },
  // 打印区设置
  blankArea: {
    width: 50, // 打印区宽度(mm)
    position: 'left', // 位置：left 或 right
  },
  // 二维码设置
  qrCode: {
    size: 60, // 二维码大小(px)
    position: 'top', // 二维码位置：left, right, top, bottom
  },
  // 门票信息
  info: {
    ticketCode: true,
    ticketNumber: true,
    personTime: true,
    entryDate: true,
    timeSlot: true,
    seat: true,
    purchaseTime: false,
  },
});

// 计算预览区样式 (1mm = 3.78px，约等于4px)
const previewStyle = computed(() => {
  const scale = 4; // 1mm = 4px，更接近实际尺寸
  const totalWidth = config.page.width * scale;
  const totalHeight = config.page.height * scale;

  return {
    width: `${totalWidth}px`,
    height: `${totalHeight}px`,
  };
});

// 计算背景图片的样式
const backgroundStyle = computed(() => {
  const scale = 4;
  const imageWidth = (config.page.width - config.blankArea.width) * scale;
  const imageHeight = config.page.height * scale;

  return {
    width: `${imageWidth}px`,
    height: `${imageHeight}px`,
  };
});

// 计算门票打印区域的样式
const infoAreaStyle = computed(() => {
  const scale = 4;
  const blankAreaWidth = config.blankArea.width * scale;

  return {
    width: `${blankAreaWidth}px`,
    height: `${config.page.height * scale}px`,
    padding: `${config.page.marginTop * scale}px ${config.page.marginRight * scale}px ${config.page.marginBottom * scale}px ${config.page.marginLeft * scale}px`,
    fontSize: `${Math.max(8, Math.min(config.page.height * 0.15, 14))}px`,
  };
});

// 计算当前显示的图片源
const currentImageSrc = computed(() => {
  // 如果用户上传了图片，使用上传的图片
  if (
    config.ticketImg &&
    Array.isArray(config.ticketImg) &&
    config.ticketImg.length > 0
  ) {
    const firstImage = config.ticketImg[0] as any;
    if (firstImage && firstImage.url) {
      return firstImage.url;
    }
  }
  // 否则使用默认图片
  return ticketBg;
});

// 计算二维码是否显示在指定位置
const showQrCodeAt = computed(() => ({
  left: config.qrCode.position === 'left',
  right: config.qrCode.position === 'right',
  top: config.qrCode.position === 'top',
  bottom: config.qrCode.position === 'bottom',
}));

watch(
  () => config,
  (newConfig) => {
    const scale = 4;
    const width = newConfig.page.width * scale;
    const height = newConfig.page.height * scale;

    emit('update:value', {
      margins: [
        newConfig.page.marginLeft * scale,
        newConfig.page.marginTop * scale,
        newConfig.page.marginRight * scale,
        newConfig.page.marginBottom * scale,
      ],
      width: width,
      height: height,
      templateConfig: newConfig,
    });
  },
  { deep: true, immediate: true },
);

watch(
  () => props.value,
  (newValue) => {
    if (newValue && newValue.templateConfig) {
      const templateConfig = newValue.templateConfig;

      // 安全地更新配置，只更新存在的字段
      if (templateConfig.ticketImg !== undefined) {
        config.ticketImg = templateConfig.ticketImg;
      }

      if (templateConfig.page) {
        // 确保 page 配置结构正确（横向需要 width 和 height）
        if (templateConfig.page.width !== undefined) {
          config.page.width = templateConfig.page.width;
        }
        if (templateConfig.page.height !== undefined) {
          config.page.height = templateConfig.page.height;
        }
        if (templateConfig.page.marginTop !== undefined) {
          config.page.marginTop = templateConfig.page.marginTop;
        }
        if (templateConfig.page.marginBottom !== undefined) {
          config.page.marginBottom = templateConfig.page.marginBottom;
        }
        if (templateConfig.page.marginLeft !== undefined) {
          config.page.marginLeft = templateConfig.page.marginLeft;
        }
        if (templateConfig.page.marginRight !== undefined) {
          config.page.marginRight = templateConfig.page.marginRight;
        }
      }

      if (templateConfig.blankArea) {
        // 确保 blankArea 配置结构正确（横向需要 width 和 left/right position）
        if (templateConfig.blankArea.width !== undefined) {
          config.blankArea.width = templateConfig.blankArea.width;
        }
        if (
          templateConfig.blankArea.position &&
          ['left', 'right'].includes(templateConfig.blankArea.position)
        ) {
          config.blankArea.position = templateConfig.blankArea.position;
        }
      }

      if (templateConfig.qrCode) {
        Object.assign(config.qrCode, templateConfig.qrCode);
      }

      if (templateConfig.info) {
        Object.assign(config.info, templateConfig.info);
      }
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

// 切换横向竖向
const toggleOrientation = () => {
  emit('toggle-orientation');
};
</script>

<template>
  <div class="flex flex-col gap-6">
    <!-- 预览区 -->
    <div class="w-full">
      <div class="mb-4 flex justify-between">
        <span class="text-lg font-semibold">模板设计</span>
        <div class="flex gap-2">
          <Button @click="toggleOrientation"> 切换为竖向 </Button>
          <CusUpload
            v-model="config.ticketImg"
            :maxCount="1"
            accept=".jpg,.png,.jpeg,.bmp,.gif,.webp"
            fileTypeTag="templateImg"
            listType="text"
            :showUploadList="false"
          >
            <Button type="primary">更换图片</Button>
          </CusUpload>
        </div>
      </div>
      <div
        class="flex min-h-[300px] items-center justify-center border border-gray-300 bg-gray-50 p-4"
      >
        <!-- 门票预览 -->
        <div class="flex bg-white shadow-lg" :style="previewStyle">
          <!-- 左侧打印区 -->
          <div
            v-if="config.blankArea.position === 'left'"
            class="flex flex-col border-r border-r-[3px] border-dashed border-black bg-white"
            :style="infoAreaStyle"
          >
            <!-- 顶部二维码 -->
            <div
              v-if="config.info.ticketCode && showQrCodeAt.top"
              class="mb-2 flex flex-shrink-0 justify-center p-0"
            >
              <CusQrCode text="门票码" :size="config.qrCode.size"></CusQrCode>
            </div>

            <div
              class="flex flex-1"
              :class="{
                'flex-row items-center':
                  showQrCodeAt.left || showQrCodeAt.right,
                'flex-col': showQrCodeAt.top || showQrCodeAt.bottom,
              }"
            >
              <!-- 左侧二维码 -->
              <div
                v-if="config.info.ticketCode && showQrCodeAt.left"
                class="mr-2 flex flex-shrink-0 items-center justify-center p-0"
              >
                <CusQrCode text="门票码" :size="config.qrCode.size"></CusQrCode>
              </div>

              <!-- 门票信息 -->
              <div class="space-y-1 text-[14px]">
                <div v-if="config.info.ticketNumber" class="flex">
                  <span
                    class="block w-[60px] text-justify opacity-50"
                    style="text-align-last: justify"
                    >票号</span
                  >
                  <span class="opacity-50">：</span>
                  <span class="flex-1">1231231231Z</span>
                </div>
                <div v-if="config.info.personTime" class="flex">
                  <span
                    class="block w-[60px] text-justify opacity-50"
                    style="text-align-last: justify"
                    >人次</span
                  >
                  <span class="opacity-50">：</span>
                  <span>2</span>
                </div>
                <div v-if="config.info.entryDate" class="flex">
                  <span
                    class="block w-[60px] text-justify opacity-50"
                    style="text-align-last: justify"
                    >入园日期</span
                  >
                  <span class="opacity-50">：</span>
                  <span>2025-07-24</span>
                </div>
                <div v-if="config.info.timeSlot" class="flex">
                  <span
                    class="block w-[60px] text-justify opacity-50"
                    style="text-align-last: justify"
                    >场次</span
                  >
                  <span class="opacity-50">：</span>
                  <span>09:00-12:00</span>
                </div>
                <div v-if="config.info.seat" class="flex">
                  <span
                    class="block w-[60px] text-justify opacity-50"
                    style="text-align-last: justify"
                    >座位</span
                  >
                  <span class="opacity-50">：</span>
                  <span>A区1排1座</span>
                </div>
                <div v-if="config.info.purchaseTime" class="flex">
                  <span
                    class="block w-[60px] flex-shrink-0 text-justify opacity-50"
                    style="text-align-last: justify"
                    >购票时间</span
                  >
                  <span class="opacity-50">：</span>
                  <span>2025-07-24 12:00:00</span>
                </div>
              </div>

              <!-- 右侧二维码 -->
              <div
                v-if="config.info.ticketCode && showQrCodeAt.right"
                class="ml-2 flex flex-shrink-0 items-center justify-center p-0"
              >
                <CusQrCode text="门票码" :size="config.qrCode.size"></CusQrCode>
              </div>
            </div>

            <!-- 底部二维码 -->
            <div
              v-if="config.info.ticketCode && showQrCodeAt.bottom"
              class="mt-2 flex flex-shrink-0 justify-center p-0"
            >
              <CusQrCode text="门票码" :size="config.qrCode.size"></CusQrCode>
            </div>
          </div>

          <!-- 背景图片 -->
          <img
            :src="currentImageSrc"
            alt="门票背景"
            class="object-cover"
            :style="backgroundStyle"
          />

          <!-- 右侧打印区 -->
          <div
            v-if="config.blankArea.position === 'right'"
            class="flex flex-col border-l border-l-[3px] border-dashed border-black bg-white"
            :style="infoAreaStyle"
          >
            <!-- 顶部二维码 -->
            <div
              v-if="config.info.ticketCode && showQrCodeAt.top"
              class="mb-2 flex flex-shrink-0 justify-center p-0"
            >
              <CusQrCode text="门票码" :size="config.qrCode.size"></CusQrCode>
            </div>

            <div
              class="flex flex-1"
              :class="{
                'flex-row': showQrCodeAt.left || showQrCodeAt.right,
                'flex-col': showQrCodeAt.top || showQrCodeAt.bottom,
              }"
            >
              <!-- 左侧二维码 -->
              <div
                v-if="config.info.ticketCode && showQrCodeAt.left"
                class="mr-2 flex flex-shrink-0 items-center justify-center p-0"
              >
                <CusQrCode text="门票码" :size="config.qrCode.size"></CusQrCode>
              </div>

              <!-- 门票信息 -->
              <div class="space-y-1 text-[14px]">
                <div v-if="config.info.ticketNumber" class="flex">
                  <span
                    class="block w-[60px] text-justify opacity-50"
                    style="text-align-last: justify"
                    >票号</span
                  >
                  <span class="opacity-50">：</span>
                  <span class="flex-1">1231231231Z</span>
                </div>
                <div v-if="config.info.personTime" class="flex">
                  <span
                    class="block w-[60px] text-justify opacity-50"
                    style="text-align-last: justify"
                    >人次</span
                  >
                  <span class="opacity-50">：</span>
                  <span>2</span>
                </div>
                <div v-if="config.info.entryDate" class="flex">
                  <span
                    class="block w-[60px] text-justify opacity-50"
                    style="text-align-last: justify"
                    >入园日期</span
                  >
                  <span class="opacity-50">：</span>
                  <span>2025-07-24</span>
                </div>
                <div v-if="config.info.timeSlot" class="flex">
                  <span
                    class="block w-[60px] text-justify opacity-50"
                    style="text-align-last: justify"
                    >场次</span
                  >
                  <span class="opacity-50">：</span>
                  <span>09:00-12:00</span>
                </div>
                <div v-if="config.info.seat" class="flex">
                  <span
                    class="block w-[60px] text-justify opacity-50"
                    style="text-align-last: justify"
                    >座位</span
                  >
                  <span class="opacity-50">：</span>
                  <span>A区1排1座</span>
                </div>
                <div v-if="config.info.purchaseTime" class="flex">
                  <span
                    class="block w-[60px] flex-shrink-0 text-justify opacity-50"
                    style="text-align-last: justify"
                    >购票时间</span
                  >
                  <span class="opacity-50">：</span>
                  <span>2025-07-24 12:00:00</span>
                </div>
              </div>

              <!-- 右侧二维码 -->
              <div
                v-if="config.info.ticketCode && showQrCodeAt.right"
                class="ml-2 flex flex-shrink-0 justify-center p-0"
              >
                <CusQrCode text="门票码" :size="config.qrCode.size"></CusQrCode>
              </div>
            </div>

            <!-- 底部二维码 -->
            <div
              v-if="config.info.ticketCode && showQrCodeAt.bottom"
              class="mt-2 flex flex-shrink-0 justify-center p-0"
            >
              <CusQrCode text="门票码" :size="config.qrCode.size"></CusQrCode>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置区 -->
    <div class="w-full">
      <div class="flex flex-col gap-6">
        <!-- 页面设置 -->
        <div>
          <h4 class="mb-3 font-semibold">页面设置</h4>
          <div class="space-y-3">
            <Row :gutter="8">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">纸张宽度：</span>
                  <InputNumber
                    v-model:value="config.page.width"
                    :min="50"
                    :max="300"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">纸张高度：</span>
                  <InputNumber
                    v-model:value="config.page.height"
                    :min="30"
                    :max="300"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
            </Row>
          </div>
        </div>

        <!-- 打印区设置 -->
        <div>
          <h4 class="mb-3 font-semibold">打印区设置</h4>
          <div class="space-y-3">
            <Row :gutter="8">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">打印区宽度：</span>
                  <InputNumber
                    v-model:value="config.blankArea.width"
                    :min="30"
                    :max="120"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">打印区位置：</span>
                  <Select
                    v-model:value="config.blankArea.position"
                    size="small"
                    style="width: 100px"
                  >
                    <Option value="left">左侧</Option>
                    <Option value="right">右侧</Option>
                  </Select>
                </div>
              </Col>
            </Row>
            <Row :gutter="8" class="mt-2">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-15 text-sm">上边距：</span>
                  <InputNumber
                    v-model:value="config.page.marginTop"
                    :min="0"
                    :max="10"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-15 text-sm">下边距：</span>
                  <InputNumber
                    v-model:value="config.page.marginBottom"
                    :min="0"
                    :max="10"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
            </Row>
            <Row :gutter="8" class="mt-2">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-15 text-sm">左边距：</span>
                  <InputNumber
                    v-model:value="config.page.marginLeft"
                    :min="0"
                    :max="10"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-15 text-sm">右边距：</span>
                  <InputNumber
                    v-model:value="config.page.marginRight"
                    :min="0"
                    :max="10"
                    size="small"
                  />
                  <span class="text-sm text-gray-500">mm</span>
                </div>
              </Col>
            </Row>
          </div>
        </div>

        <!-- 二维码设置 -->
        <div>
          <h4 class="mb-3 font-semibold">二维码设置</h4>
          <div class="space-y-3">
            <Row :gutter="8">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">二维码大小：</span>
                  <InputNumber
                    v-model:value="config.qrCode.size"
                    :min="40"
                    :max="200"
                    size="small"
                  />
                  <span class="text-sm text-gray-500"></span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-18 text-sm">二维码位置：</span>
                  <Select
                    v-model:value="config.qrCode.position"
                    size="small"
                    style="width: 100px"
                  >
                    <Option value="left">左侧</Option>
                    <Option value="right">右侧</Option>
                    <Option value="top">顶部</Option>
                    <Option value="bottom">底部</Option>
                  </Select>
                </div>
              </Col>
            </Row>
          </div>
        </div>

        <!-- 门票信息设置 -->
        <div>
          <h4 class="mb-3 font-semibold">门票信息设置</h4>
          <div class="space-y-3">
            <Row :gutter="8">
              <Col :span="6">
                <Checkbox v-model:checked="config.info.ticketCode"
                  >二维码</Checkbox
                >
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.ticketNumber"
                  >票号</Checkbox
                >
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.personTime"
                  >人次</Checkbox
                >
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.entryDate"
                  >入园日期</Checkbox
                >
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.timeSlot">场次</Checkbox>
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.seat">座位</Checkbox>
              </Col>
              <Col :span="6">
                <Checkbox v-model:checked="config.info.purchaseTime"
                  >购票时间</Checkbox
                >
              </Col>
            </Row>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
