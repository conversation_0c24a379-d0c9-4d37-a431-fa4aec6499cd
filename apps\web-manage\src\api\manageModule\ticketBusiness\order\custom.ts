import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getCustomizeOrderList(params: Recordable<any>) {
  return requestClient.get('/tkt/customizeOrder/list', { params });
}

async function getCustomizeOrderInfo(id: string) {
  return requestClient.get('/tkt/customizeOrder/info', { params: { id } });
}
async function getCustomizeOrderRefundInfo(id: string) {
  return requestClient.get('/tkt/customizeOrder/refundInfo', {
    params: { id },
  });
}

async function getCustomizeOrderLog(params: Recordable<any>) {
  return requestClient.get('/tkt/customizeOrder/log', { params });
}

async function customizeOrderRefund(data: Recordable<any>) {
  return requestClient.post('/tkt/customizeOrder/refund', data);
}

export {
  getCustomizeOrderList,
  getCustomizeOrderInfo,
  getCustomizeOrderLog,
  customizeOrderRefund,
  getCustomizeOrderRefundInfo,
};
