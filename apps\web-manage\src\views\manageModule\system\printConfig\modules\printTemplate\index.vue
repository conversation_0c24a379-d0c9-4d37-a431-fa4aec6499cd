<script setup lang="ts">
import { useVbenDrawer } from '@vben/common-ui';
import { ref } from 'vue';
import { setPrintTemplateConfig } from '#/api/manageModule';
import Ticket from './ticket.vue';
import Cashier from './cashier.vue';
import Recharge from './recharge.vue';
import Lease from './lease.vue';
import CheckTicket from './checkTicket.vue';
import GeneralTicket from './generalTicket/index.vue';
import { message } from 'ant-design-vue';

const id = ref();
const templateType = ref(1);
const templateData = ref<any>({});
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    console.log(templateData.value, 'templateData.value');
    let submitParams = {};
    if (templateType.value !== 1) {
      submitParams = {
        id: id.value,
        margins: JSON.stringify(templateData.value.margins),
        width: templateData.value.width,
        templateConfig: JSON.stringify(templateData.value.templateConfig),
      };
    } else {
      submitParams = {
        id: id.value,
        height: templateData.value.height.toFixed(0),
        margins: JSON.stringify(templateData.value.margins),
        width: templateData.value.width.toFixed(0),
        bgImage: templateData.value.templateConfig.ticketImg[0]?.url,
        templateConfig: JSON.stringify(templateData.value.templateConfig),
      };
    }
    await setPrintTemplateConfig(submitParams);
    message.success('保存成功');
    drawerApi.close();
    console.log('onConfirm', submitParams);
  },
  onOpenChange(isOpen) {
    const data = drawerApi.getData<any>();
    if (isOpen) {
      id.value = data.id;
      templateType.value = data.templateType;
      templateData.value = {
        margins: JSON.parse(data.margins),
        width: data.width,
        templateConfig: JSON.parse(data.templateConfig),
      };
    }
  },
});
</script>

<template>
  <Drawer class="w-[1200px]" title="打印配置">
    <GeneralTicket v-model:value="templateData" v-if="templateType === 1" />
    <Ticket v-model:value="templateData" v-if="templateType === 2" />
    <Cashier v-model:value="templateData" v-if="templateType === 3" />
    <Lease v-model:value="templateData" v-if="templateType === 4" />
    <Recharge v-model:value="templateData" v-if="templateType === 5" />
    <CheckTicket v-model:value="templateData" v-if="templateType === 6" />
  </Drawer>
</template>
