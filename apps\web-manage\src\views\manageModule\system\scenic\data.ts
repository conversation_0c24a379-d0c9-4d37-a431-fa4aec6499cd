import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

// 搜索表单
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '景区名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入景区名称',
        allowClear: true,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'scenicName',
      title: '景区名称',
      width: 150,
      fixed: 'left',
    },
    {
      field: 'scenicImage',
      title: '景区图片',
      width: 150,
      slots: {
        default: 'CellImages',
      },
    },
    {
      field: 'businessHours',
      minWidth: 100,
      title: '营业时间',
    },
    {
      field: 'address',
      minWidth: 100,
      title: '景区地址',
    },
    {
      field: 'contact',
      minWidth: 100,
      title: '联系方式',
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 180,
    },
    {
      field: 'status',
      maxWidth: 100,
      title: '状态',
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: onStatusChange,
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'scenicName',
          nameTitle: '景区',
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 130,
    },
  ];
}
