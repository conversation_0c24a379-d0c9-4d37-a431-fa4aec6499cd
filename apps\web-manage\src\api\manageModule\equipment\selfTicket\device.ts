import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getSelfTicketList(params: Recordable<any>) {
  return requestClient.get('/eqp/self/list', { params });
}
async function getAllSelfTicketList(params: Recordable<any>) {
  return requestClient.get('/eqp/self/all', { params });
}
async function getSelfTicketInfo(id: string) {
  return requestClient.get('/eqp/self/info', { params: { id } });
}

async function createSelfTicket(data: Recordable<any>) {
  return requestClient.post('/eqp/self/create', data);
}

async function updateSelfTicket(data: Recordable<any>) {
  return requestClient.post('/eqp/self/update', data);
}

async function deleteSelfTicket(id: string) {
  return requestClient.post('/eqp/self/delete', { id });
}

async function changeSelfTicketStatus(data: Recordable<any>) {
  return requestClient.post('/eqp/self/changeStatus', data);
}

async function getSelfTicketOrderList(params: any) {
  return requestClient.get('/eqp/self/ticketOrder', { params });
}

export {
  getSelfTicketList,
  getAllSelfTicketList,
  getSelfTicketInfo,
  createSelfTicket,
  updateSelfTicket,
  deleteSelfTicket,
  changeSelfTicketStatus,
  getSelfTicketOrderList,
};
