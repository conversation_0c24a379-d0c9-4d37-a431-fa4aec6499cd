import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getCardList(params: Recordable<any>) {
  return requestClient.get('/tkt/card/list', { params });
}

async function getCardInfo(params: Recordable<any>) {
  return requestClient.get('/tkt/card/info', { params });
}
async function getCardInfoByNo(params: Recordable<any>) {
  return requestClient.get('/tkt/card/infoByCardNo', { params });
}

async function createCard(data: Recordable<any>) {
  return requestClient.post('/tkt/card/create', data);
}
async function rechargeCard(data: Recordable<any>) {
  return requestClient.post('/tkt/card/recharge', data);
}

async function replaceCard(data: Recordable<any>) {
  return requestClient.post('/tkt/card/replace', data);
}
async function refundCard(data: Recordable<any>) {
  return requestClient.post('/tkt/card/refund', data);
}
async function changeCardStatus(data: Recordable<any>) {
  return requestClient.post('/tkt/card/changeStatus', data);
}

async function amountChange(data: Recordable<any>) {
  return requestClient.post('/tkt/card/change', data);
}

async function tradeLog(params: Recordable<any>) {
  return requestClient.get('/tkt/card/log', { params });
}
async function rechargeOrder(params: Recordable<any>) {
  return requestClient.get('/tkt/card/rechargeOrder', { params });
}

export {
  getCardList,
  getCardInfo,
  getCardInfoByNo,
  createCard,
  rechargeCard,
  changeCardStatus,
  replaceCard,
  refundCard,
  amountChange,
  tradeLog,
  rechargeOrder,
};
