<script setup lang="ts">
import { ref, } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useEditFormSchema } from '../data';
import { updateCoupon } from '#/api/manageModule';
import { message } from 'ant-design-vue';

const emit = defineEmits(['success']);
const formData = ref<any>({});
// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    labelWidth: 120,
    componentProps: {
      class: 'w-full',
    },
  },
  fieldMappingTime: [['thresholdTypes', ['thresholdType', 'thresholdPrice'], null]],
  //   scrollToFirstError: true,
  schema: useEditFormSchema(),
  showDefaultActions: false,
  // 一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2',
});
// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    const data = modelApi.getData<any>();
    formApi.resetForm();
    if (isOpen) {
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };
        newFormData.thresholdTypes = [data.thresholdType, data.thresholdPrice];
        // 赋值给formData.value
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      }
    }
  },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();

  return values;
};

// 统一的提交处理函数
const handleSubmit = async () => {
  const values = await processFormValues();
  console.log(values, 'submit');
  if (!values) return;
  modelApi.lock();
  try {
    await updateCoupon({ id: id.value, ...values });
    message.success('操作成功');
    emit('success');
    modelApi.close();
  } catch (error) {
    modelApi.unlock();
  }
};
</script>
<template>
  <Model class="w-[70%]" title="编辑优惠券">
    <Form></Form>
  </Model>
</template>
