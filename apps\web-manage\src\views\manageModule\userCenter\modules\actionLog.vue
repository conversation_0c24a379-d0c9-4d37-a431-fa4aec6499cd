<script setup lang="ts">
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { actionColumns, actionGridFormSchema } from '../data';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getAdminUserActionLogList } from '#/api/manageModule';
import { RangePicker, Button } from 'ant-design-vue';
import dayjs from 'dayjs';
import { ref } from 'vue';
import Info from './actionLogInfo.vue';

const [Drawer, drawerApi] = useVbenDrawer({
  connectedComponent: Info,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['date', ['beginDate', 'endDate']]],
    schema: actionGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },
  separator: false,
  gridOptions: {
    columns: actionColumns(),
    height: '99%',
    keepSource: true,
    headerCellConfig: {
      height: 40,
    },
    cellConfig: {
      height: 70,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res: any = await getAdminUserActionLogList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
  } as VxeTableGridOptions<any>,
});

const onShowInfo = (row: any) => {
  drawerApi.setData(row).open();
};

// 日期范围限制
const startDate = ref(null);

const onCalendarChange = (dates: any) => {
  if (dates && dates.length > 0) {
    startDate.value = dates[0];
  } else {
    startDate.value = null;
  }
};

const disabledDate = (current: any) => {
  // 不能选择今天之后的日期
  if (current && current >= dayjs().endOf('day')) {
    return true;
  }

  // 如果已经选择了开始日期，限制结束日期范围
  if (startDate.value && current) {
    const start = dayjs(startDate.value);
    const diffDays = current.diff(start, 'day');

    // 限制结束日期不能超过开始日期后30天
    if (diffDays > 30) {
      return true;
    }

    // 限制结束日期不能超过开始日期前30天
    if (diffDays < -30) {
      return true;
    }
  }

  return false;
};
</script>
<template>
  <div class="h-full w-full">
    <Drawer></Drawer>
    <Grid>
      <template #form-date="slotProps">
        <RangePicker
          v-bind="slotProps"
          :disabledDate="disabledDate"
          @calendarChange="onCalendarChange"
        />
      </template>
      <template #action="{ row }">
        <Button type="link" size="small" @click="onShowInfo(row)">详情</Button>
      </template>
    </Grid>
  </div>
</template>
