import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-dashboard',
      order: -1,
      title: $t('page.dashboard.title'),
    },
    name: 'Dashboard',
    path: '/dashboard',
    children: [
      {
        name: 'Analytics',
        path: '/analytics',
        component: () => import('#/views/manageModule/dashboard/analytics/index.vue'),
        meta: {
          affixTab: true,
          ignoreAccess: true,
          keepAlive: true,
          icon: 'lucide:area-chart',
          title: $t('page.dashboard.analytics'),
          query: '',
        },
      },
      {
        name: 'Workspace',
        path: '/workspace',
        component: () => import('#/views/manageModule/dashboard/workspace/index.vue'),
        meta: {
          ignoreAccess: true,
          icon: 'carbon:workspace',
          keepAlive: true,
          title: $t('page.dashboard.workspace'),
          query: '',
        },
      },
    ],
  },
];

export default routes;
